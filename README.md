# Project structure

- Public graphql: graphql for user authentication, hyperliquid agent wallet
- Internal grpc: internal grpc for vault key management, signing order transaction

# Running local environment

#### Requirement

1. NodeJS version 22.4.0
2. Docker

#### Startup application

1. Run stateful components
```
$ docker-compose up -d
```

2. Run Internal grpc app
```
$ export STAGE=local && yarn run start:dev internal-grpc
```

3. Run User public graphql
```
$ export STAGE=local && yarn run start:dev public-graphql
```

# Development Scenario

### Database migration

1. Define Entity first

2. Generate migration sql
```
$ npx mikro-orm migration:create
```

3. Apply migration to database
```
$ npx mikro-orm migration:up
```

### gRPC development 
1. Install buf cli

2. Define proto file first

3. Generate gRPC Client/Server
```
$ buf generate
```

4. Use generated gRPC code as interface to implement business logic

## Deployment commands


### Apply migration in k8s
```
$ MIKRO_ORM_CLI_USE_TS_NODE=false npx mikro-orm migration:up --config ./dist/apps/public-graphql/mikro-orm.config.js
```

### user-service (public graphql)

```
cmd: ["node", "start:public-graphql"]
port: 3000
```

### user-grpc (internal grpc)

```
cmd: ["node", "start:grpc-internal"]
port: 5001
```
