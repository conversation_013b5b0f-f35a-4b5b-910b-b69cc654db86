export const tokenDetailQuery = `
query GetTokenTelegram($tokenAddress: String!, $walletAddress: String) {
    getTokenTelegram(
      input: {
        tokenAddress: $tokenAddress
        walletAddress: $walletAddress
      }
    ) {
      address
        chainId
        symbol
        name
        decimals
        totalSupply
        tags
        mintDisable
        isBlacklisted
        isHoneypot
        burnRatio
        burnStatus
        top10HolderRate
        ratTraderAmountRate
        price
        marketCap
        info {
            logoUrl
            websites {
                url
                label
            }
            socials {
                url
                type
            }
        }
        walletToken {
            address
            token
            balance
            buys
            sells
            rawBalance
            createdAt
            updatedAt
            chainId
            avgPriceUsd
            avgMarketCap
            symbol
            totalUsdValue
            totalBuyQty
            totalBuyUsd
            totalSellQty
            totalSellUsd
            totalTradedQty
            realizedPnL
            nativeBalance
        }
        dexes
    }
  }
`;

export const getNetworkFeeQuery = `
  query getNetworkFee($input: NetworkFeeInput!) {
    getNetworkFee(input: $input) {
      solana {
        priorityFeePrice {
          medium
          high
          veryHigh
        }
        maxComputeUnits
      }
      ethereum {
        low {
          suggestedMaxPriorityFeePerGas
          suggestedMaxFeePerGas
          minWaitTimeEstimate
          maxWaitTimeEstimate
        }
        medium {
          suggestedMaxPriorityFeePerGas
          suggestedMaxFeePerGas
          minWaitTimeEstimate
          maxWaitTimeEstimate
        }
        high {
          suggestedMaxPriorityFeePerGas
          suggestedMaxFeePerGas
          minWaitTimeEstimate
          maxWaitTimeEstimate
        }
        estimatedBaseFee
      }
    }
  }`;
export const createOrderMutation = `
  mutation createOrder($input: CreateOrderInput!) {
    createOrder(input: $input) {
      id
      createdAt
      updatedAt
      deletedAt
      transactionType
      type
      baseAddress
      quoteAddress
      userAddress
      limitPrice
      baseAmount
      quoteAmount
      exit
      tp
      sl
      exitAt
      status
      txid
      chainId
      baseDecimal
      baseSymbol
      quoteSymbol
      slippage
    }
  }
`;
