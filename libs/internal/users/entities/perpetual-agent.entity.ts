import { Entity, EntityRepositoryType, Enum, Index, ManyToOne, PrimaryKey, Property, Unique } from '@mikro-orm/core';
import { Field, HideField, ObjectType } from '@nestjs/graphql';
import { User } from './user.entity';
import { uuidv7 } from 'uuidv7';
import { PerpetualAgentRepository } from '../repositories/perpetual-agent.repository';

@ObjectType()
@Entity({ repository: () => PerpetualAgentRepository })
@Index({ properties: ['agentAddress'] })
export class PerpetualAgent {
    [EntityRepositoryType]?: PerpetualAgentRepository;

    @Field()
    @PrimaryKey({ type: 'uuid' })
    id: string = uuidv7();

    @ManyToOne(() => User)
    user: User;

    @Field()
    @Property()
    userAddress: string;

    @Property({ unique: true, default: false })
    agentAddress: string;

    @Property({ nullable: true, type: 'bigint' })
    expiredAt: number | null;

    @HideField()
    @Property({ length: 511, lazy: true })
    encryptedPrivateKey: string;

    @Property({ defaultRaw: 'now()' })
    createdAt: Date = new Date();
}
