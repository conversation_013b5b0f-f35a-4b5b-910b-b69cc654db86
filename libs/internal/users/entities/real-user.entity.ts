import { <PERSON><PERSON>ty, PrimaryKey, Unique, OneToMany, Collection } from '@mikro-orm/core';
import { Field, ObjectType } from '@nestjs/graphql';
import { uuidv7 } from 'uuidv7';
import { User } from './user.entity';
import { RealUserRepository } from '../repositories/real-user.repository';
import { UserDevice } from './user-device.entity';

@ObjectType()
@Entity({ repository: () => RealUserRepository })
export class RealUser {
    @Field()
    @PrimaryKey({ type: 'uuid' })
    id: string = uuidv7();

    @Field(() => [User])
    @OneToMany(() => User, (user) => user.realUser)
    users: Collection<User> = new Collection<User>(this);

    @Field(() => [UserDevice])
    @OneToMany(() => UserDevice, (userDevice) => userDevice.realUser)
    userDevices: Collection<UserDevice> = new Collection<UserDevice>(this);
}
