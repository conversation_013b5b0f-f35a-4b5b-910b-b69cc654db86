import { Entity, EntityRepositoryType, Enum, ManyToOne, PrimaryKey, Property, Unique } from '@mikro-orm/core';
import { Field, ObjectType } from '@nestjs/graphql';
import { User } from './user.entity';
import { uuidv7 } from 'uuidv7';
import { UserPerpetualStatusRepository } from '../repositories/user-perpetual-status.repository';

@ObjectType()
@Entity({ repository: () => UserPerpetualStatusRepository })
@Unique({ properties: ['user'] })
export class UserPerpetualStatus {
    [EntityRepositoryType]?: UserPerpetualStatusRepository;

    @Field()
    @PrimaryKey({ type: 'uuid' })
    id: string = uuidv7();

    @ManyToOne(() => User)
    user: User;

    @Property({ default: false })
    setReferral: boolean;

    @Property({ type: 'varchar', length: 55, nullable: true })
    referralCode: string;

    @Property({ default: false })
    setFeeBuilder: boolean;

    @Property({ type: 'varchar', length: 255, nullable: true })
    feeBuilderAddress: string;

    @Property({ type: 'decimal(12,10)', nullable: true })
    feeBuilderPercent: number;

    @Property({ defaultRaw: 'now()' })
    createdAt: Date = new Date();
}
