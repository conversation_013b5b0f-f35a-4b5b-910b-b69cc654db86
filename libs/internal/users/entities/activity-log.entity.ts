import { Entity, ManyToOne, Property } from '@mikro-orm/core';
import { PrimaryKey } from '@mikro-orm/core';
import { Field } from '@nestjs/graphql';
import { uuidv7 } from 'uuidv7';
import { UserDevice } from './user-device.entity';
import { User } from './user.entity';
import { ActivityLogRepository } from '../repositories/activity-log.repository';

@Entity({ repository: () => ActivityLogRepository })
export class ActivityLog {
    @Field()
    @PrimaryKey({ type: 'uuid' })
    id: string = uuidv7();

    @Field(() => User)
    @ManyToOne(() => User)
    user: User;

    @Field(() => UserDevice, { nullable: true })
    @ManyToOne(() => UserDevice, { nullable: true })
    device?: UserDevice;

    @Field()
    @Property()
    activity: string;

    @Field()
    @Property()
    createdAt: Date = new Date();
}
