import { En<PERSON>ty, ManyToOne, PrimaryKey, Property, Unique, Collection, OneToMany, Index } from '@mikro-orm/core';
import { Field } from '@nestjs/graphql';
import { uuidv7 } from 'uuidv7';
import { User } from './user.entity';
import { ActivityLog } from './activity-log.entity';
import { UserDeviceRepository } from '../repositories/user-device.repository';
import { RealUser } from './real-user.entity';

@Entity({ repository: () => UserDeviceRepository })
@Unique({ properties: ['user', 'fingerprint'] })
export class UserDevice {
    @Field()
    @PrimaryKey({ type: 'uuid' })
    id: string = uuidv7();

    @Field(() => User)
    @ManyToOne(() => User)
    user: User;

    @Field()
    @Property()
    @Index({ type: 'hash' })
    fingerprint: string;

    @Field()
    @Property()
    createdAt: Date = new Date();

    @OneToMany(() => ActivityLog, (activityLog) => activityLog.device)
    activityLogs = new Collection<ActivityLog>(this);

    @ManyToOne(() => RealUser, { nullable: true })
    realUser?: RealUser;
}
