import { <PERSON><PERSON>ty, ManyToOne, PrimaryKey, Property, Unique, Collection, OneToMany, OneToOne } from '@mikro-orm/core';
import { <PERSON>, HideField } from '@nestjs/graphql';
import { uuidv7 } from 'uuidv7';
import { User } from './user.entity';
import { SubOrganizationAuthRepository } from '../repositories/sub-organization-auth.repository';

@Entity({ repository: () => SubOrganizationAuthRepository })
export class SubOrganizationAuth {
    @Field()
    @PrimaryKey({ type: 'uuid' })
    id: string = uuidv7();

    @Field(() => User)
    @OneToOne(() => User)
    user: User;

    @Property({ length: 511, unique: true })
    encryptedPrivateKey: string;

    @Property({ length: 511, unique: true })
    publicKey: string;

    @Property({ nullable: false, type: 'uuid', unique: true })
    subOrgId: string;

    @Field()
    @Property()
    createdAt: Date = new Date();
}
