import { Module } from '@nestjs/common';
import { UsersService } from './users.service';
import { MikroOrmModule } from '@mikro-orm/nestjs';
import { User } from './entities/user.entity';
import { UserManagedWallet } from './entities/user-managed-wallet.entity';
import { WalletModule } from '../wallet/wallet.module';
import { UserPerpetualStatus } from './entities/user-perpetual-status.entity';
import { PerpetualAgent } from './entities/perpetual-agent.entity';
import { UserInfoService } from './user-info.service';
import { ActivityLog } from './entities/activity-log.entity';
import { UserDevice } from './entities/user-device.entity';
import { RealUser } from './entities/real-user.entity';
import { SettingsModule } from '../settings/setting.module';
import { UserEmbeddedWallet } from './entities/user-embedded-wallet.entity';
import { SubOrganizationAuth } from './entities/sub-organization-auth.entity';

@Module({
    providers: [UsersService, UserInfoService],
    exports: [UsersService, UserInfoService],
    imports: [
        MikroOrmModule.forFeature({
            entities: [
                User,
                UserManagedWallet,
                UserPerpetualStatus,
                PerpetualAgent,
                UserDevice,
                ActivityLog,
                RealUser,
                UserEmbeddedWallet,
                SubOrganizationAuth,
            ],
        }),
        WalletModule,
        SettingsModule,
    ],
})
export class UsersModule {}
