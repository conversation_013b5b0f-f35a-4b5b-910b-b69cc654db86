import { generateRandomEthWallet, generateRandomSolanaWallet, generateRandomTronWallet } from './wallet.utils';

describe('Wallet Utils', () => {
    test('generateRandomEthWallet should return a valid Ethereum wallet', () => {
        const wallet = generateRandomEthWallet();
        expect(wallet).toHaveProperty('address');
        expect(wallet).toHaveProperty('privateKey');
        expect(wallet.address).toMatch(/^0x[a-fA-F0-9]{40}$/);
        expect(wallet.privateKey).toMatch(/^0x[a-fA-F0-9]{64}$/);
    });

    test('generateRandomSolanaWallet should return a valid Solana wallet', () => {
        const wallet = generateRandomSolanaWallet();
        expect(wallet).toHaveProperty('address');
        expect(wallet).toHaveProperty('privateKey');
        expect(wallet.address.length).toBeGreaterThan(30); // Solana address is usually ~44 chars
        expect(wallet.privateKey.length).toBeGreaterThan(60); // Hex-encoded private key
    });

    test('generateRandomTronWallet should return a valid Tron wallet', async () => {
        const wallet = await generateRandomTronWallet();
        expect(wallet).toHaveProperty('address');
        expect(wallet).toHaveProperty('privateKey');
        expect(wallet.address).toMatch(/^T[a-zA-Z0-9]{33}$/); // Tron address starts with T and is 34 chars long
        expect(wallet.privateKey.length).toBe(64); // Private key should be 64 hex characters
    });
});
