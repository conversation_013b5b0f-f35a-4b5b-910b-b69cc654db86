import { EntityManager } from '@mikro-orm/core';
import { UsersService } from './users.service';
import {
    ChainType,
    GetUserBalanceRequest,
    GetUserInfoRequest,
    UserBanlanceResponse,
    WalletManagedResponse,
} from '@protogen/user/v1/user_info';
import { UserManagedWallet } from './entities/user-managed-wallet.entity';
import { getSolanaBalance } from './wallet.utils';
import { Injectable } from '@nestjs/common';
import { solToLamport } from 'libs/common/utils/helpers2';

@Injectable()
export class UserInfoService {
    constructor(
        private readonly userService: UsersService,
        private readonly em: EntityManager,
    ) {}

    async getUserWalletManaged(request: GetUserInfoRequest): Promise<WalletManagedResponse | null> {
        if (!request.userId && !request.telegramId) {
            return null;
        }
        const user = await this.userService.getUserInfoByTelegram(request);
        const wallets = await this.em.fork().find(UserManagedWallet, { user: user });
        const chainMap: Record<string, ChainType> = {
            EVM: ChainType.EVM,
            SOLANA: ChainType.SOLANA,
            TRON: ChainType.TRON,
            ARB: ChainType.ARB,
        };

        return {
            wallets: wallets.map((i) => ({
                id: i.id,
                userId: i.user.id,
                chain: chainMap[i.chain],
                walletAddress: i.walletAddress,
            })),
        };
    }
    async getUserBalance(request: GetUserBalanceRequest): Promise<UserBanlanceResponse | null> {
        if (!request.walletAddress) {
            return null;
        }
        const balance = await getSolanaBalance(request.walletAddress);
        return {
            balance: balance,
        };
    }
}
