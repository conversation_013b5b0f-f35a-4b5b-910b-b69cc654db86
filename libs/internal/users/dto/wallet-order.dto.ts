import { Field, InputType, ObjectType } from '@nestjs/graphql';
import { IsNotEmpty, IsUUID, IsArray, ValidateNested, IsInt, Min, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';

export enum WalletType {
    EMBEDDED = 'EMBEDDED',
    MANAGED = 'MANAGED',
}

@InputType()
export class WalletOrderItem {
    @Field(() => String, { description: 'Wallet ID' })
    @IsUUID('4', { message: 'Wallet ID must be a valid UUID' })
    @IsNotEmpty({ message: 'Wallet ID is required' })
    id: string;

    @Field(() => Number, { description: 'Display order (0-based index)' })
    @IsInt({ message: 'Display order must be an integer' })
    @Min(0, { message: 'Display order must be non-negative' })
    displayOrder: number;

    @Field(() => String, { description: 'Wallet type (EMBEDDED or MANAGED)' })
    @IsEnum(WalletType, { message: 'Wallet type must be either EMBEDDED or MANAGED' })
    type: WalletType;
}

@InputType()
export class UpdateWalletOrderInputDTO {
    @Field(() => [WalletOrderItem], { description: 'List of wallet IDs with their new display orders' })
    @IsArray({ message: 'Wallets must be an array' })
    @ValidateNested({ each: true })
    @Type(() => WalletOrderItem)
    wallets: WalletOrderItem[];
}

@ObjectType()
export class UpdateWalletOrderResponseDTO {
    @Field(() => Boolean, { description: 'Whether the update was successful' })
    success: boolean;

    @Field(() => String, { description: 'Success or error message' })
    message: string;

    @Field(() => Number, { description: 'Number of wallets updated' })
    updatedCount: number;
}
