import { Field, InputType, ObjectType } from '@nestjs/graphql';
import { ChainType } from '../entities/user-managed-wallet.entity';
import { UserEmbeddedWallet } from '../entities/user-embedded-wallet.entity';
import { IsNotEmpty, IsString, IsUUID, Length, Matches } from 'class-validator';
import { WALLET_NAME_MIN_LENGTH, WALLET_NAME_MAX_LENGTH, WALLET_NAME_REGEX } from '../user.constants';

export class EmbeddedWalletDto {
    walletAddress: string;
    chain: ChainType;
    walletId: string;
    walletAccountId: string;
    hdPath: string;
    name: string;
}

@ObjectType()
export class UserEmbeddedWalletDTO extends UserEmbeddedWallet {
    @Field(() => Number)
    balance: number;

    @Field(() => String)
    walletId: string;

    @Field(() => String)
    walletAccountId: string;

    @Field(() => String)
    name: string;
}

@InputType()
export class UpdateEmbeddedWalletNameInputDTO {
    @Field(() => String, { description: 'User Embedded Wallet ID' })
    @IsUUID('4', { message: 'User Embedded Wallet ID must be a valid UUID' })
    @IsNotEmpty({ message: 'User Embedded Wallet ID is required' })
    id: string;

    @Field(() => String, { description: 'New wallet name' })
    @IsString({ message: 'Wallet name must be a string' })
    @IsNotEmpty({ message: 'Wallet name cannot be empty' })
    @Length(WALLET_NAME_MIN_LENGTH, WALLET_NAME_MAX_LENGTH, {
        message: `Wallet name must be between ${WALLET_NAME_MIN_LENGTH} and ${WALLET_NAME_MAX_LENGTH} characters`,
    })
    @Matches(WALLET_NAME_REGEX, {
        message: 'Wallet name can only contain letters, numbers, spaces, hyphens, underscores, and dots',
    })
    name: string;
}

@ObjectType()
export class UpdateEmbeddedWalletNameResponseDTO {
    @Field(() => Boolean, { description: 'Whether the update was successful' })
    success: boolean;

    @Field(() => String, { description: 'Success or error message' })
    message: string;

    @Field(() => UserEmbeddedWallet, { nullable: true, description: 'Updated wallet information' })
    wallet?: UserEmbeddedWallet | null;
}
