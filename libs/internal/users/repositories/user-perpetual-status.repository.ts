import { Entity<PERSON>anager, EntityRepository } from '@mikro-orm/core';
import { User } from '../entities/user.entity';
import { UserPerpetualStatusDTO } from '../dto/perpetual-status.dto';
import { UserPerpetualStatus } from '../entities/user-perpetual-status.entity';
import { stat } from 'fs';

export class UserPerpetualStatusRepository extends EntityRepository<UserPerpetualStatus> {
    async createOrUpdateStatus(
        em: EntityManager,
        user: User,
        statusDto: UserPerpetualStatusDTO,
    ): Promise<UserPerpetualStatus> {
        let status = await this.findOne({ user });

        if (!status) {
            status = new UserPerpetualStatus();
            status.user = user;
        }

        status.setReferral = statusDto.setReferral;
        status.setFeeBuilder = statusDto.setFeeBuilder;
        status.referralCode = statusDto.referralCode;
        status.feeBuilderAddress = statusDto.feeBuilderAddress;
        status.feeBuilderPercent = statusDto.feeBuilderPercent;

        await em.persistAndFlush(status);

        return status;
    }
}
