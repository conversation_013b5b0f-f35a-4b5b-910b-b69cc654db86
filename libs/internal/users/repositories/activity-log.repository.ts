import { <PERSON>tity<PERSON>anager, EntityRepository } from '@mikro-orm/core';
import { ActivityLog } from '../entities/activity-log.entity';
import { User } from '../entities/user.entity';
import { UserDevice } from '../entities/user-device.entity';

export class ActivityLogRepository extends EntityRepository<ActivityLog> {
    createActivityLog(em: EntityManager, user: User, activity: string, device?: UserDevice): ActivityLog {
        const activityLog = new ActivityLog();
        activityLog.user = user;
        activityLog.device = device;
        activityLog.activity = activity;
        em.persist(activityLog);
        return activityLog;
    }
}
