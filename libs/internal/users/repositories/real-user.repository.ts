import { <PERSON><PERSON>tyManager, EntityRepository } from '@mikro-orm/core';
import { RealUser } from '../entities/real-user.entity';
import { User } from '../entities/user.entity';
import { UserDevice } from '../entities/user-device.entity';

export class RealUserRepository extends EntityRepository<RealUser> {
    async getOrCreateRealUser(
        em: EntityManager,
        user: User,
        device?: UserDevice,
    ): Promise<{ realUser: RealUser; isNewRealUser: boolean }> {
        if (device) {
            const realUser = await em.findOne(
                RealUser,
                { userDevices: { fingerprint: device.fingerprint } },
                { populate: ['users', 'userDevices'] },
            );
            if (realUser) {
                // match this user to the real user
                const userExists = realUser.users.contains(user);
                if (!userExists) {
                    realUser.users.add(user);
                    user.realUser = realUser;
                    em.persistAndFlush(realUser);
                }
                // match this device to the real user
                const deviceExists = realUser.userDevices.contains(device);
                if (!deviceExists) {
                    realUser.userDevices.add(device);
                    em.persistAndFlush(realUser);
                }
                return { realUser, isNewRealUser: false };
            }
        }

        const realUser = await em.findOne(RealUser, { users: { id: user.id } }, { populate: ['userDevices'] });
        if (realUser) {
            // match this device to the real user
            if (device) {
                const userDevice = realUser.userDevices.find((d) => d.id === device.id);
                if (!userDevice) {
                    realUser.userDevices.add(device);
                    em.persistAndFlush(realUser);
                }
            }
            return { realUser, isNewRealUser: false };
        }

        const newRealUser = new RealUser();
        newRealUser.users.add(user);
        if (device) {
            newRealUser.userDevices.add(device);
        }
        em.persistAndFlush(newRealUser);
        return { realUser: newRealUser, isNewRealUser: true };
    }
}
