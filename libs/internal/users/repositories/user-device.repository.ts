import { EntityManager, EntityRepository } from '@mikro-orm/core';
import { UserDevice } from '../entities/user-device.entity';
import { User } from '../entities/user.entity';

export class UserDeviceRepository extends EntityRepository<UserDevice> {
    async getOrCreateUserDevice(em: EntityManager, user: User, fingerprint: string): Promise<UserDevice> {
        const userDevice = await em.findOne(UserDevice, { user: user, fingerprint: fingerprint });
        if (userDevice) {
            return userDevice;
        }

        const newUserDevice = new UserDevice();
        newUserDevice.user = user;
        newUserDevice.fingerprint = fingerprint;
        em.persistAndFlush(newUserDevice);
        return newUserDevice;
    }
}
