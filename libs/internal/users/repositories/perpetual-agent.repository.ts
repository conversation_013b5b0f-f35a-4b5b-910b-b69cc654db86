import { Entity<PERSON>anager, EntityRepository } from '@mikro-orm/core';
import { User } from '../entities/user.entity';
import { PerpetualAgent } from '../entities/perpetual-agent.entity';
import { PerpetualAgentDto, UpdatePerpetualAgentDto } from '../dto/perpetual-agent.dto';
import { Update } from 'telegraf/typings/core/types/typegram';

export class PerpetualAgentRepository extends EntityRepository<PerpetualAgent> {
    async createAgentForUser(em: EntityManager, user: User, agent: PerpetualAgentDto): Promise<PerpetualAgent> {
        const agentEntity = new PerpetualAgent();

        agentEntity.userAddress = agent.userAddress;
        agentEntity.user = user;
        agentEntity.encryptedPrivateKey = agent.encryptedPrivateKey;
        agentEntity.agentAddress = agent.agentAddress;

        await em.persistAndFlush(agentEntity);

        return agentEntity;
    }

    async updateAgentForUser(em: <PERSON>ti<PERSON><PERSON><PERSON><PERSON>, user: User, agent: UpdatePerpetualAgentDto): Promise<PerpetualAgent> {
        const agentEntity = await this.findOne({ user: user });

        if (!agentEntity) {
            throw new Error('Agent not found');
        }

        agentEntity.expiredAt = agent.expiredAt;

        await em.persistAndFlush(agentEntity);

        return agentEntity;
    }
}
