import { Entity<PERSON>anager, EntityRepository } from '@mikro-orm/core';
import { User } from '../entities/user.entity';
import { SubOrganizationAuthDto } from '../dto/sub-organization-auth.dto';
import { SubOrganizationAuth } from '../entities/sub-organization-auth.entity';
import { rsaEncrypt } from './utils';

export class SubOrganizationAuthRepository extends EntityRepository<SubOrganizationAuth> {
    async createSubOrganizationAuth(
        em: EntityManager,
        user: User,
        params: SubOrganizationAuthDto,
    ): Promise<SubOrganizationAuth> {
        const subOrganizationAuth = await em.findOne(SubOrganizationAuth, { user: user });
        if (subOrganizationAuth) {
            return subOrganizationAuth;
        }

        const newSubOrgAuth = new SubOrganizationAuth();
        newSubOrgAuth.user = user;
        newSubOrgAuth.subOrgId = params.subOrgId;
        newSubOrgAuth.publicKey = params.publicKey;
        newSubOrgAuth.encryptedPrivateKey = rsaEncrypt(params.privateKey, `${params.subOrgId}-${user.id}`);
        em.persistAndFlush(newSubOrgAuth);
        return newSubOrgAuth;
    }
}
