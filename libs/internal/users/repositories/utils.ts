import { REFERRER_CODE_LENGTH } from '../user.constants';
import * as crypto from 'crypto';

const IV_LENGTH = 12;

function getKey(secret: string): Buffer {
    return crypto.createHash('sha256').update(secret).digest();
}

export function generateReferrerCode(): string {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';

    for (let i = 0; i < REFERRER_CODE_LENGTH; i++) {
        result += characters.charAt(Math.floor(Math.random() * characters.length));
    }

    return result;
}

export function rsaEncrypt(text: string, secret: string): string {
    const iv = crypto.randomBytes(IV_LENGTH);
    const key = getKey(secret);

    const cipher = crypto.createCipheriv('aes-256-gcm', key, iv);
    const encrypted = Buffer.concat([cipher.update(text, 'utf8'), cipher.final()]);
    const tag = cipher.getAuthTag();

    return Buffer.concat([iv, encrypted, tag]).toString('base64');
}

export function rsaDecrypt(encryptedText: string, secret: string): string {
    const data = Buffer.from(encryptedText, 'base64');
    const key = getKey(secret);

    const iv = Buffer.from(data.subarray(0, IV_LENGTH)); // Replaces slice(0, IV_LENGTH)
    const tag = Buffer.from(data.subarray(data.length - 16)); // Replaces slice(data.length - 16)
    const encrypted = Buffer.from(data.subarray(IV_LENGTH, data.length - 16)); // Replaces slice(IV_LENGTH, data.length - 16)

    const decipher = crypto.createDecipheriv('aes-256-gcm', key, iv);
    decipher.setAuthTag(tag);

    const decrypted = Buffer.concat([decipher.update(encrypted), decipher.final()]);
    return decrypted.toString('utf8');
}
