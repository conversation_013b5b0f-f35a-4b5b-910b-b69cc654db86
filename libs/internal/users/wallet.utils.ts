import { clusterApiUrl, Connection, Keypair, PublicKey } from '@solana/web3.js';
import { ethers } from 'ethers';
import { TronWeb } from 'tronweb';
import { appConfig } from '../../configs';

export function generateRandomEthWallet() {
    const wallet = ethers.Wallet.createRandom();
    return {
        address: wallet.address,
        privateKey: wallet.privateKey,
    };
}

export function generateRandomSolanaWallet() {
    const keypair = Keypair.generate();
    return {
        address: keypair.publicKey.toBase58(),
        privateKey: Buffer.from(keypair.secretKey).toString('hex'),
    };
}

export async function generateRandomTronWallet() {
    const tronWeb = new TronWeb({ fullHost: 'https://api.trongrid.io' });
    const account = tronWeb.createAccount();

    return await account.then((acc) => ({
        address: acc.address.base58, // Tron địa chỉ ở dạng base58
        privateKey: acc.privateKey,
    }));
}

export async function getEvmBalance(walletAddress: string): Promise<number> {
    const provider = new ethers.JsonRpcProvider(appConfig.INFURA_RPC_URL);

    try {
        const balance = await provider.getBalance(walletAddress);
        return Number(ethers.formatEther(balance));
    } catch (err) {
        console.error(`Error fetching EVM balance for ${walletAddress}:`, err);
        return 0;
    } finally {
        provider.destroy();
    }
}

export async function getArbBalance(walletAddress: string): Promise<number> {
    const provider = new ethers.JsonRpcProvider('https://arb1.arbitrum.io/rpc');

    try {
        const balance = await provider.getBalance(walletAddress);
        return Number(ethers.formatEther(balance));
    } catch (err) {
        console.error(`Error fetching Arb balance for ${walletAddress}:`, err);
        return 0;
    } finally {
        provider.destroy();
    }
}

export async function getSolanaBalance(walletAddress: string): Promise<number> {
    try {
        const connection = new Connection(clusterApiUrl('mainnet-beta'));
        const address = new PublicKey(walletAddress);
        const balance = await connection.getBalance(address);
        return Number(balance / 1e9);
    } catch (err) {
        console.error(`Error fetching SOL balance for ${walletAddress}:`, err);
        return 0;
    }
}

export async function getTronBalance(walletAddress: string): Promise<number> {
    try {
        const tronWeb = new TronWeb({
            fullHost: 'https://api.trongrid.io',
            headers: { 'TRON-PRO-API-KEY': appConfig.TRON_GRID_API_KEY },
        });
        const balance = await tronWeb.trx.getBalance(walletAddress);
        return Number(balance / 1e6);
    } catch (error) {
        console.warn(`Error fetching TRON balance for ${walletAddress}:`, error);
        return 0;
    }
}
