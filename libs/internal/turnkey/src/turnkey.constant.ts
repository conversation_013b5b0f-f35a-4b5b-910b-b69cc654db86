export const TURNKEY_OTP_LENGTH = 6;
export const TURNKEY_OTP_ALPHANUMERIC = false;
export const TURNKEY_OTP_TTL = 600; // in seconds

export enum RANGO_CONTRACT_WHITELIST {
    Ethereum = '******************************************',
    BobaEth = '******************************************',
    ZkSyncEra = '******************************************',
}

export enum MEME_ADDRESS_WHITELIST {
    Jupiter = 'JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4',
    Orca = '9W959DqEETiGZocYWCQPaJ6sBmUzgfxXfqGeTEdp3aQP',
    OrcaTokenSwap = 'DjVE6JNiYqPL2QXyCUUh8rNjHrbz9hXHNYt99MQ59qw1',
    Mercurial = 'MERLuDFBMmsHnsBPZw2sDQZHvXFMwp8EdjudcU2HKky',
    Serum = '9xQeWvG816bUx9EPjHmaT23yvVM2ZWbrrpZb9PusVFin',
    Raydium = '675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8',
    Saber = 'SSwpkEEcbUqx4vtoEByFjSkhKdCT862DNVb52nZg1UZ',
    Penguin = 'PSwapMdSai8tjrEXcxFeQth87xC4rRsa4VA5mhGhXkP',
    Aldrin = 'AMM55ShdkoGRB5jVYPjWziwk8m5MpwyDgsMWHaMSQWH6',
    AldrinV2 = 'CURVGoZn8zycx6FXwwevgBTB2gVvdbGTEpvMJDbgs2t4',
    Step = 'SSwpMgqNDsyV7mAgN9ady4bDVu5ySjmmXejXvy2vLt1',
    Cropper = 'CTMAxxk34HjKWxQ3QLZK1HpaLXmBveao3ESePXbiyfzh',
    Sencha = 'SCHAtsf8mbjyjiv4LkhLKutTf6JnZAbdJKFkXQNMFHZ',
    Crema = 'CLMM9tUoggJu2wagPkkqs9eFG4BWhVBZWkP1qv3Sp7tR',
    Lifinity = 'EewxydAPCCVuNEyrVN68PuSYdQ7wKn27V9Gjeoi8dy3S',
    Saros = 'SSwapUtytfBdBn1b9NUGG6foMVPtcWgpRU32HToDUZr',
    Whirlpool = 'whirLbMiicVdio4qvUfM5KAg6Ct8VwpYzGff3uctyCc',
    Cykura = 'cysPXAjehMpVKUapzbMCCnpFxUFFryEWEaLgnb9NrR8',
    Marinade = 'MarBmsSgKXdrN1egZf5sqe1TMai9K1rChYNDJgjq7aD',
    Stepn = 'Dooar9JkhdZ7J3LHN3A7YCuoGRUggXhQaG4kijfLGU2j',
    Meteora = 'Eo7WjKq67rjJQSZxS6z3YkapzY3eMj6Xy8X5EQVn5UaB',
    Invariant = 'HyaB3W9q6XdA5xwpU4XnSZV94htfmbmqJXZcEbRaJutt',
    GooseFX = '7WduLbRfYhTJktjLw5FDEyrqoEv61aTTCuGAetgLjzN5',
    SaberDecimalWrapper = 'DecZY86MU5Gj7kppfUCEmd4LbXXuyZH1yHaP2NTqdiZB',
    Balansol = 'D3BBjqUdCYuP18fNvvMbPAZ8DpcRi4io2EsYHQawJDag',
    Dradex = 'dp2waEWSBy5yKmq65ergoU3G6qRLmqa6K7We4rZSKph',
    LifinityV2 = '2wT8Yq49kHgDzXuPxZSaeLaH1qbmGXtEyPy64bL7aD3c',
    RaydiumCLMM = 'CAMMCzo5YL8w4VFF8KVHrK22GGUsp5VTaW7grrKgrWqK',
    Openbook = 'srmqPvymJeFKQ4zGQed1GFppgkRHL9kaELCbyksJtPX',
    MarcoPolo = '9tKE7Mbmj4mxDjWatikzGAtkoWosiiZX9y6J4Hfm2R8H',
    Phoenix = 'PhoeNiXZ8ByJGLkxNfZRnkUfjvmuYqLR89jjFHGqdXY',
    Symmetry = '2KehYt3KsEQR53jYcxjbQp2d2kCp4AkuQW68atufRwSr',
    BonkSwap = 'BSwp6bEBihVLdqJRKGgzjcGLHkcTuzmSo1TQkHepzH8p',
    FluxBeam = 'FLUXubRmkEi2q6K3Y9kBPg9248ggaZVsoSFhtJHSrm1X',
    HeliumNetwork = 'treaf4wWBBty3fHdyBpo35Mz84M8k3heKXmjmi9vFt5',
    unstakeIt = 'stkitrT1Uoy18Dk1fTrgPw8W6MVzoCfYoAFT4MLsmhq',
    GooseFXV2 = 'GFXsSL5sSaDfNFQUYsHekbWBW1TsFdjDYzACh62tEHxn',
    Perps = 'PERPHjGBqRHArX4DySjwM6UJHiR3sWAatqfdBS2qQJu',
    MeteoraDLMM = 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo',
    TokenSwap = 'SwaPpA9LAaLfeLi3a68M4DjnLqgtticKg6CnyNwgAC8',
    OpenbookV2 = 'opnb2LAfJYbRMAHHvqjCwQxanZn7ReEHp1k81EohpZb',
    Dexlab = 'DSwpgjMvXhtGn6BsbqmacdBZyfLj6jSWf3HJpdJtmg6N',
    ObricV2 = 'obriQD1zbpyLz95G5n7nJe6a4DPjpFwa5XYPoNm113y',
    Pumpfun = '6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P',
    PumpAmm = 'pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA',
    Moonshot = 'MoonCVVNZFSYkqNXP6bxHLPL6QQJiMagDL3qcqUQTrG',
    RayCpmm = 'CPMMoo8L3F4NbTegBCKVNunggL7H1ZpdTHKxQB5qKP1C',
    RayLaunchLab = 'LanMV9sAd7wArD4vJFi2qDdfnVhFxYSUg6eADduJ3uj',
    RayStableSwapAMM = '5quBtoiQqxF9Jv6KYKctB59NT3gtJD2Y65kdnB1Uev3h',
    RayAmmRouting = 'routeUGWgWzqBWFcrCfv8tritsqukccJPu3q5GPP3xS',
}

export const MAX_SOLANA_WALLET_ACCOUNTS = 10;

export const ROOT_ORG_NAME = 'Root Org';
export const MAIN_ACCOUNT_NAME = 'Main Account';
