import { randomBytes } from 'crypto';
import { p256 } from '@noble/curves/p256';

const getPublicKey = (privateKey: Uint8Array | string, isCompressed: boolean = true): Uint8Array => {
    return p256.getPublicKey(privateKey, isCompressed);
};

export const generateP256KeyPair = () => {
    const privateKey = randomBytes(32);
    const publicKey = getPublicKey(privateKey, true);

    return {
        privateKey: privateKey.toString('hex'),
        publicKey: Buffer.from(publicKey).toString('hex'),
    };
};
