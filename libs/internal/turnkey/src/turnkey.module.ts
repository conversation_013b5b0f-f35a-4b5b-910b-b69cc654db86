import { Module } from '@nestjs/common';
import { TurnkeyService } from './turnkey.service';
import { UsersModule } from 'libs/internal/users/users.module';
import { JwtModule } from '@nestjs/jwt';
import { appConfig } from 'libs/configs';
import { AuthModule } from 'libs/internal/auth/auth.module';
import { RateLimitingModule } from 'libs/internal/rate-limiting';

@Module({
    providers: [TurnkeyService],
    exports: [TurnkeyService],
    imports: [
        JwtModule.register({
            secret: appConfig.JWT_SECRET,
            signOptions: { expiresIn: appConfig.ACCESS_TOKEN_EXPIRES_IN },
        }),
        UsersModule,
        AuthModule,
        RateLimitingModule,
    ],
})
export class TurnkeyModule {}
