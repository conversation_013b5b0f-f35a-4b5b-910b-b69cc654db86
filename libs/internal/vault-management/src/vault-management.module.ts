import { Module } from '@nestjs/common';
import { VaultManagementService } from './vault-management.service';
import { VaultAgentWallet } from './entities/vault-agent-wallet.entity';
import { VaultMasterWallet } from './entities/vault-master-wallet.entity';
import { MikroOrmModule } from '@mikro-orm/nestjs';

@Module({
    imports: [MikroOrmModule.forFeature({ entities: [VaultAgentWallet, VaultMasterWallet] })],
    providers: [VaultManagementService],
    exports: [VaultManagementService],
})
export class VaultManagementModule {}
