import { Entity<PERSON>anager, EntityRepository } from '@mikro-orm/core';
import { VaultMasterWallet } from '../entities/vault-master-wallet.entity';
import { CreateVaultMasterDto } from '../dto/create-vault-master.dto';

export class VaultMasterWalletRepository extends EntityRepository<VaultMasterWallet> {
    async createWalletsForUser(em: EntityManager, wallet: CreateVaultMasterDto): Promise<VaultMasterWallet> {
        const w = new VaultMasterWallet();
        w.address = wallet.address;
        w.encryptedPrivateKey = wallet.encryptedPrivateKey;
        await em.persistAndFlush(w);

        return w;
    }
}
