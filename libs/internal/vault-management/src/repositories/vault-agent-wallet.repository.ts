import { Entity<PERSON>anager, EntityRepository } from '@mikro-orm/core';
import { UserManagedWallet } from '../../../users/entities/user-managed-wallet.entity';
import { VaultMasterWallet } from '../entities/vault-master-wallet.entity';
import { VaultAgentWallet } from '../entities/vault-agent-wallet.entity';
import { CreateVaultAgentDto } from '../dto/create-vault-agent.dto';

export class VaultAgentWalletRepository extends EntityRepository<VaultAgentWallet> {
    async createAgentWalletForMaster(
        em: EntityManager,
        master: VaultMasterWallet,
        wallet: CreateVaultAgentDto,
    ): Promise<VaultAgentWallet> {
        // const em = this.em.fork();

        const agent = new VaultAgentWallet();
        agent.vaultMasterWallet = master;
        agent.agentAddress = wallet.agentAddress;
        agent.encryptedPrivateKey = wallet.encryptedPrivateKey;
        agent.expiredAt = wallet.expiredAt;

        await em.persistAndFlush(agent);

        return agent;
    }
}
