import { EntityManager } from '@mikro-orm/core';
import { Injectable } from '@nestjs/common';
import { VaultMasterWalletRepository } from './repositories/vault-master-wallet.repository';
import { VaultAgentWalletRepository } from './repositories/vault-agent-wallet.repository';
import { CreateVaultMasterDto } from './dto/create-vault-master.dto';
import { VaultMasterWallet } from './entities/vault-master-wallet.entity';
import { CreateVaultAgentDto } from './dto/create-vault-agent.dto';
import { VaultAgentWallet } from './entities/vault-agent-wallet.entity';

@Injectable()
export class VaultManagementService {
    constructor(
        private readonly em: EntityManager,
        private readonly vaultMasterWalletRepository: VaultMasterWalletRepository,
        private readonly vaultAgentWalletRepository: VaultAgentWalletRepository,
    ) {}

    async createMasterWallet(wallet: CreateVaultMasterDto): Promise<VaultMasterWallet> {
        const forkedEm = this.em.fork();
        return forkedEm.transactional(async (txEm) => {
            return this.vaultMasterWalletRepository.createWalletsForUser(txEm, wallet); // Use txEm
        });
    }

    async createAgentWallet(master: VaultMasterWallet, wallet: CreateVaultAgentDto): Promise<VaultAgentWallet> {
        const forkedEm = this.em.fork();
        return forkedEm.transactional(async (txEm) => {
            return this.vaultAgentWalletRepository.createAgentWalletForMaster(txEm, master, wallet); // Use txEm
        });
    }

    getMasterWalletById(id: string) {
        return this.em.fork().findOne(VaultMasterWallet, id, {
            populate: ['encryptedPrivateKey'],
        });
    }

    getAgentWalletByMasterWallet(masterWalletId: string) {
        return this.em.fork().findOne(
            VaultAgentWallet,
            {
                vaultMasterWallet: {
                    id: masterWalletId,
                },
            },
            {
                populate: ['encryptedPrivateKey'],
            },
        );
    }
}
