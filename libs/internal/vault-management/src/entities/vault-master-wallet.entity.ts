import { Entity, EntityRepositoryType, PrimaryKey, Property, Unique } from '@mikro-orm/core';
import { Field, HideField, ObjectType } from '@nestjs/graphql';
import { uuidv7 } from 'uuidv7';
import { VaultMasterWalletRepository } from '../repositories/vault-master-wallet.repository';

@ObjectType()
@Entity({ repository: () => VaultMasterWalletRepository })
export class VaultMasterWallet {
    [EntityRepositoryType]?: VaultMasterWalletRepository;

    @Field()
    @PrimaryKey({ type: 'uuid' })
    id: string = uuidv7();

    @Field()
    @Property({ unique: true })
    address: string;

    @HideField()
    @Property({ length: 511, lazy: true })
    encryptedPrivateKey: string;

    @Property({ defaultRaw: 'now()' })
    createdAt: Date = new Date();
}
