import { Entity, EntityRepositoryType, OneToOne, PrimaryKey, Property, Unique } from '@mikro-orm/core';
import { Field, HideField, ObjectType } from '@nestjs/graphql';
import { uuidv7 } from 'uuidv7';
import { VaultMasterWallet } from './vault-master-wallet.entity';
import { VaultAgentWalletRepository } from '../repositories/vault-agent-wallet.repository';

@ObjectType()
@Entity({ repository: () => VaultAgentWalletRepository })
export class VaultAgentWallet {
    [EntityRepositoryType]?: VaultAgentWalletRepository;

    @Field()
    @PrimaryKey({ type: 'uuid' })
    id: string = uuidv7();

    @OneToOne(() => VaultMasterWallet)
    vaultMasterWallet: VaultMasterWallet;

    @Field()
    @Property({ unique: true })
    agentAddress: string;

    @HideField()
    @Property({ length: 511, lazy: true })
    encryptedPrivateKey: string;

    @Property({ nullable: true, type: 'bigint' })
    expiredAt: number | null;

    @Property({ defaultRaw: 'now()' })
    createdAt: Date = new Date();
}
