import { RedisService } from '@lib/redis';
import { Injectable } from '@nestjs/common';
import { createCache, Cache } from 'cache-manager';

@Injectable()
export class CachingService {
    private memoryCache: Cache;
    constructor(private redisService: RedisService) {
        this.memoryCache = createCache();
    }

    async generateAuthCode(userId: string): Promise<string> {
        const code = crypto.randomUUID().toString();
        const key = `auth:code:${userId}`;
        await this.redisService.getClient().set(key, code, 'EX', 1000);
        return code;
    }

    async getAuthCode(userId: string): Promise<string | null> {
        return this.redisService.getClient().get(`auth:code:${userId}`);
    }
}
