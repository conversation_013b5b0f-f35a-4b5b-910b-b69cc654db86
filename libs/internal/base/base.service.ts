import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import axios, { AxiosRequestConfig } from 'axios';
import { ServiceError } from '../../common/api-errors';
import { appConfig } from '../../configs';
import { PinoLogger } from 'nestjs-pino';

@Injectable()
export class BaseService {
    private readonly logger = new PinoLogger({ renameContext: BaseService.name });

    async call<T>(
        method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH',
        url: string,
        data: any,
        config: AxiosRequestConfig = {},
        retry_time: number = 0,
    ): Promise<T | undefined> {
        try {
            const response = await axios({
                method: method,
                url: url,
                data: data,
                ...config,
            });
            return response.status !== 204 ? (response.data as T) : undefined;
        } catch (error) {
            if (error.response) {
                throw new ServiceError(url, error.response.status, error.response.data);
            }
            if (retry_time > appConfig.RETRY_THRESHOLD) {
                this.logger.error({ url, retry_time }, `Max retry attempts reached time retry. Throwing error.`);
                throw new HttpException('Unknown API error', HttpStatus.INTERNAL_SERVER_ERROR);
            }
            const delay = Math.pow(2, retry_time) * 200;
            await new Promise((r) => setTimeout(r, delay));
            return this.call<T>(method, url, data, config, retry_time + 1);
        }
    }
}
