import { EntityManager } from '@mikro-orm/core';
import { InjectRepository } from '@mikro-orm/nestjs';
import { UserGoogleAuthenticator } from 'libs/internal/settings/entities/user-google-authenticator.entity';
import { UserGoogleAuthenticatorRepository } from 'libs/internal/settings/repositories/user-google-authenticator.repository';
import * as speakeasy from 'speakeasy';

export class AuthService {
    constructor(
        private readonly em: EntityManager,
        @InjectRepository(UserGoogleAuthenticator)
        private readonly userGoogleAuthenticatorRepository: UserGoogleAuthenticatorRepository,
    ) {}

    async verifyTOTP(userId: string, code: string): Promise<boolean> {
        const auth = await this.userGoogleAuthenticatorRepository.findByUserId(this.em, userId);
        if (!auth) {
            throw new Error('2FA is not setup');
        }
        if (!auth.isEnabled) {
            throw new Error('2FA is not enabled');
        }

        const isValid = speakeasy.totp.verify({
            secret: auth.secretKey,
            token: code,
            window: 1,
            encoding: 'base32',
        });

        return isValid;
    }
}
