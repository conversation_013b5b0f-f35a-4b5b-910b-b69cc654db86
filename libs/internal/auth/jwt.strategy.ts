import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { appConfig } from '../../configs';
import { JwtClaim } from './auth.vo';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
    constructor() {
        super({
            jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
            ignoreExpiration: false,
            secretOrKey: appConfig.JWT_SECRET,
        });
    }

    async validate(payload: any): Promise<JwtClaim> {
        return payload as JwtClaim; // TODO: remove other field
    }
}
