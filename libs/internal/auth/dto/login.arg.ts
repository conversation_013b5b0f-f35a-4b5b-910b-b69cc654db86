import { ArgsType, Field } from '@nestjs/graphql';
import { ChainType } from '../../users/entities/user-managed-wallet.entity';

@ArgsType()
export class LoginArgs {
    @Field(() => String)
    message: string;

    @Field(() => String)
    signature: string;

    @Field(() => ChainType)
    chainType: ChainType;

    @Field(() => Boolean, { defaultValue: false })
    isOkxWallet: boolean;

    @Field(() => String, { nullable: true })
    referrerCode?: string;
    @Field(() => String, { nullable: true })
    fingerprint?: string;
}
