import { Field, Float, InputType, ObjectType } from '@nestjs/graphql';
import { ChainType } from 'libs/internal/users/entities/user-managed-wallet.entity';

@InputType()
export class GetWalletSubOrgInputDTO {
    @Field(() => String, { description: 'Request signing message' })
    message: string;

    @Field(() => String, { description: 'Signature of message' })
    signature: string;

    @Field(() => Boolean, { description: 'Using okx wallet to signing', defaultValue: false })
    isOkxWallet: boolean;

    @Field(() => ChainType)
    chainType: ChainType;

    @Field(() => String, { nullable: true })
    referrerCode?: string;
    @Field(() => String, { nullable: true })
    fingerprint?: string;
}

@InputType()
export class GetGoogleSubOrgInputDTO {
    @Field()
    idToken: string;

    @Field({ nullable: true })
    fingerprint?: string;

    @Field({ nullable: true })
    referrerCode?: string;

    @Field(() => String, { description: 'Target public key' })
    targetPublicKey: string;
}

@InputType()
export class GetTelegramSubOrgInputDTO {
    @Field(() => Float)
    id: number;

    @Field(() => String)
    firstName: string;

    @Field(() => String)
    username: string;

    @Field(() => String)
    photoUrl: string;

    @Field(() => Float)
    authDate: number;

    @Field(() => String)
    hash: string;
}

@ObjectType()
export class SubOrgResponseDTO {
    @Field(() => String)
    subOrgId: string;

    @Field(() => String)
    userId: string;

    @Field(() => String, { nullable: true, description: 'Turnkey session expiration in seconds' })
    sessionExpiresIn?: string;
}

@InputType()
export class InputLoginWalletV2Dto {
    @Field(() => String, { description: 'Sub Organization ID' })
    organizationId: string;

    @Field(() => String, { description: 'Public key to address Turnkey session' })
    publicKey: string;

    @Field(() => String, { description: 'Session expire time in seconds' })
    expirationSeconds: string;

    @Field(() => String, { description: 'Request timestamp in milliseconds' })
    timestampMs: string;

    @Field(() => String, { description: 'Stamp header name' })
    stampHeaderName: string;

    @Field(() => String, { description: 'Stamp header value' })
    stampHeaderValue: string;

    @Field(() => String, { description: 'URL for the API call' })
    url: string;
}

@InputType()
export class InputLoginGoogleDto extends InputLoginWalletV2Dto {
    @Field(() => String, { description: 'Timestamp in milliseconds' })
    timestampMs: string;

    @Field(() => String, { description: 'ID Token' })
    idToken: string;

    @Field(() => String, { description: 'Target public key' })
    targetPublicKey: string;
}

@ObjectType()
export class LoginV2DTO {
    @Field()
    accessToken: string;
    @Field()
    refreshToken: string;
    @Field()
    userId: string;
    @Field(() => String, { nullable: true })
    referrerCode?: string;
    @Field(() => String, { nullable: true })
    fingerprint?: string;

    @Field(() => String)
    subOrgId?: string;

    @Field(() => String)
    turnKeyResponse: string;

    @Field({ nullable: true })
    deletedAt?: Date;
}

@ObjectType()
export class TurnKeyCredentialResponseDto {
    @Field(() => String)
    session: string;
}

@ObjectType()
export class LoginGoogleDTO {
    @Field()
    accessToken: string;
    @Field()
    refreshToken: string;
    @Field()
    userId: string;
    @Field(() => String, { nullable: true })
    referrerCode?: string;
    @Field(() => String, { nullable: true })
    fingerprint?: string;

    @Field(() => String)
    subOrgId?: string;

    @Field(() => TurnKeyCredentialResponseDto)
    turnKeyResponse: TurnKeyCredentialResponseDto;

    @Field({ nullable: true })
    deletedAt?: Date;
}

@ObjectType()
export class TurnKeyLogEmailOtpResponseDto {
    @Field(() => String)
    session: string;
}

@ObjectType()
export class LoginEmailOtpDTO {
    @Field()
    accessToken: string;
    @Field()
    refreshToken: string;
    @Field()
    userId: string;
    @Field(() => String, { nullable: true })
    referrerCode?: string;
    @Field(() => String, { nullable: true })
    fingerprint?: string;

    @Field(() => String)
    subOrgId?: string;

    @Field(() => TurnKeyLogEmailOtpResponseDto)
    turnKeyResponse: TurnKeyLogEmailOtpResponseDto;

    @Field({ nullable: true })
    deletedAt?: Date;
}

// OTP Authentication DTOs for GraphQL
@InputType()
export class GetEmailSubOrgInputDTO {
    @Field(() => String, { description: 'Email address' })
    email: string;

    @Field(() => String, { nullable: true })
    referrerCode?: string;

    @Field(() => String, { nullable: true })
    fingerprint?: string;
}

@InputType()
export class InitEmailOtpInputDTO {
    @Field(() => String, { description: 'Email address' })
    email: string;

    @Field(() => String, { nullable: true })
    referrerCode?: string;

    @Field(() => String, { nullable: true })
    fingerprint?: string;
}

@ObjectType()
export class InitEmailOtpResponseDTO {
    @Field(() => String, { description: 'OTP ID for verification' })
    otpId: string;

    @Field(() => String, { description: 'User ID in the system' })
    userId: string;

    @Field(() => String, { description: 'Sub-organization ID' })
    subOrgId: string;
}

@InputType()
export class LoginWithEmailOtpInputDTO {
    @Field(() => String, { description: 'Email address' })
    email: string;

    @Field(() => String, { description: 'OTP ID from initialization' })
    otpId: string;

    @Field(() => String, { description: 'OTP code received by user' })
    otpCode: string;

    @Field(() => String, { description: 'Target public key for credential encryption' })
    targetPublicKey: string;
}
