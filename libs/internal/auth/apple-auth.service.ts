import { Injectable } from '@nestjs/common';
import { appConfig } from '../../configs';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';
import { ApiError } from 'libs/common/api-errors';
import { INVALID_APPLE_LOGIN, TOKEN_EXPIRED } from 'libs/common/api-errors/errors';
import { JwksClient } from 'jwks-rsa';
import * as jwt from 'jsonwebtoken';

@Injectable()
export class AppleAuthService {
    private client: JwksClient;

    constructor(
        @InjectPinoLogger(AppleAuthService.name)
        private readonly logger: PinoLogger,
    ) {
        this.client = new JwksClient({
            jwksUri: 'https://appleid.apple.com/auth/keys',
            cache: true,
            rateLimit: true,
        });
    }

    async verifyAppleToken(idToken: string) {
        try {
            const decodedHeader = jwt.decode(idToken, { complete: true });

            if (!decodedHeader || typeof decodedHeader === 'string') {
                throw new ApiError(INVALID_APPLE_LOGIN);
            }

            const kid = decodedHeader.header.kid;
            const alg = decodedHeader.header.alg as jwt.Algorithm;

            const key = await this.client.getSigningKey(kid);
            const publicKey = key.getPublicKey();

            const payload = jwt.verify(idToken, publicKey, {
                algorithms: [alg],
                issuer: 'https://appleid.apple.com',
                audience: appConfig.APPLE_CLIENT_ID, // Or from appConfig
            }) as jwt.JwtPayload;

            const currentTime = Math.floor(Date.now() / 1000);
            const expTime = payload.exp ?? 0;

            if (!expTime || currentTime >= expTime) {
                this.logger.warn({ currentTime, expTime, expired: currentTime >= expTime }, 'Apple token has expired');
                throw new ApiError(TOKEN_EXPIRED);
            }

            return {
                sub: payload.sub,
                email: payload.email || '',
                name: '', // Apple only provides name on first login via frontend
                picture: '', // Apple doesn't provide avatar
            };
        } catch (error) {
            this.logger.warn({ error, idToken }, 'Failed to verify Apple token');
            throw new ApiError(INVALID_APPLE_LOGIN);
        }
    }
}
