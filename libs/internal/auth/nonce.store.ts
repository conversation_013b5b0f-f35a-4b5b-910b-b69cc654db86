import { Injectable } from '@nestjs/common';

@Injectable()
export class NonceStore {
    private nonces = new Map<string, string>(); // TODO: move to redis

    setNonce(walletAddress: string, nonce: string) {
        this.nonces.set(walletAddress.toLowerCase(), nonce);
    }

    getNonce(walletAddress: string): string | undefined {
        return this.nonces.get(walletAddress.toLowerCase());
    }

    deleteNonce(walletAddress: string) {
        this.nonces.delete(walletAddress.toLowerCase());
    }
}
