import { Injectable } from '@nestjs/common';
import { OAuth2Client } from 'google-auth-library';
import { appConfig } from '../../configs';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';
import { ApiError } from 'libs/common/api-errors';
import { INVALID_GOOGLE_LOGIN, TOKEN_EXPIRED } from 'libs/common/api-errors/errors';

@Injectable()
export class GoogleAuthService {
    private oauth2Client: OAuth2Client;

    constructor(
        @InjectPinoLogger(GoogleAuthService.name)
        private readonly logger: PinoLogger,
    ) {
        this.oauth2Client = new OAuth2Client(
            appConfig.GOOGLE_CLIENT_ID,
            appConfig.GOOGLE_CLIENT_SECRET,
            appConfig.GOOGLE_CALLBACK_URL,
        );
    }

    async verifyGoogleToken(idToken: string) {
        try {
            const ticket = await this.oauth2Client.verifyIdToken({
                idToken,
                audience: appConfig.GOOGLE_CLIENT_ID,
            });

            const payload = ticket.getPayload();

            if (!payload) {
                throw new ApiError(INVALID_GOOGLE_LOGIN);
            }

            // Check expiration time
            const currentTime = Math.floor(Date.now() / 1000); // Current time in seconds
            const expTime = payload.exp; // Expiration time from token

            if (!expTime || currentTime >= expTime) {
                this.logger.warn(
                    {
                        currentTime,
                        expTime,
                        expired: currentTime >= expTime,
                    },
                    'Google token has expired',
                );
                throw new ApiError(TOKEN_EXPIRED);
            }

            return {
                sub: payload.sub,
                email: payload.email,
                name: payload.name,
                picture: payload.picture,
            };
        } catch (error) {
            this.logger.warn(error, 'Failed to verify Google token');
            throw new ApiError(INVALID_GOOGLE_LOGIN);
        }
    }
}
