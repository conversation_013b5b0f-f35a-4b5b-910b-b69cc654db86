import { Test, TestingModule } from '@nestjs/testing';
import { WalletAuthService } from './wallet-auth.service';
import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../users/users.service';
import { NonceStore } from './nonce.store';
import { ethers } from 'ethers';
import * as nacl from 'tweetnacl';
import { Keypair } from '@solana/web3.js';
import { decodeUTF8 } from 'tweetnacl-util';
import { TronWeb } from 'tronweb';

describe('WalletAuthService', () => {
    let walletAuthService: WalletAuthService;
    let jwtService: JwtService;
    let userService: UsersService;
    let nonceStore: NonceStore;

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                WalletAuthService,
                {
                    provide: JwtService,
                    useValue: {
                        signAsync: jest.fn().mockResolvedValue('mocked-jwt-token'),
                    },
                },
                {
                    provide: UsersService,
                    useValue: {
                        getOrCreateUserByWallet: jest.fn().mockResolvedValue({ id: 1 }),
                    },
                },
                {
                    provide: NonceStore,
                    useValue: {
                        setNonce: jest.fn(),
                        getNonce: jest.fn(),
                    },
                },
            ],
        }).compile();

        walletAuthService = module.get<WalletAuthService>(WalletAuthService);
        jwtService = module.get<JwtService>(JwtService);
        userService = module.get<UsersService>(UsersService);
        nonceStore = module.get<NonceStore>(NonceStore);
    });

    it('should be defined', () => {
        expect(walletAuthService).toBeDefined();
    });

    describe('generateNonce', () => {
        it('should generate a random nonce and store it', async () => {
            const walletAddress = '0x123456789abcdef';
            const nonce = walletAuthService.generateNonce(walletAddress);

            expect(nonce).toBeDefined();
            expect(nonce).toHaveLength(34); // 16 bytes hex encoded
            expect(nonceStore.setNonce).toHaveBeenCalledWith(walletAddress, nonce);
        });
    });

    describe('getSignMessage', () => {
        it('should generate a valid sign-in message', () => {
            const walletAddress = '0x123456789abcdef';
            const nonce = '0xabcdef123456';

            const message = walletAuthService.getSignMessage(walletAddress, nonce);

            expect(message).toContain(walletAddress);
            expect(message).toContain(nonce);
            expect(message).toContain('Welcome to Xbit!');
        });
    });

    describe('extractWalletAddress', () => {
        it('should correctly extract the wallet address from the message', () => {
            const walletAddress = '0x123456789abcdef';
            const nonce = '0xabcdef123456';

            const message = walletAuthService.getSignMessage(walletAddress, nonce);
            const extractedAddress = walletAuthService.extractWalletAddress(message);

            expect(extractedAddress).toBe(walletAddress);
        });
    });

    describe('verifyEVMSignature', () => {
        it('should return true for a valid signature', async () => {
            const wallet = ethers.Wallet.createRandom();
            const message = 'Test message';
            const signature = await wallet.signMessage(message);

            const isValid = walletAuthService.verifyEVMSignature(message, signature, wallet.address);
            expect(isValid).toBe(true);
        });

        it('should return false for an invalid signature', async () => {
            const wallet = ethers.Wallet.createRandom();
            const anotherWallet = ethers.Wallet.createRandom();
            const message = 'Test message';
            const signature = await anotherWallet.signMessage(message); // Ký bằng ví khác

            const isValid = walletAuthService.verifyEVMSignature(message, signature, wallet.address);
            expect(isValid).toBe(false);
        });
    });

    describe('okxMessageToSign', () => {
        it('should generate a trimmed OKX sign-in message with correct address and nonce', () => {
            const address = '0x123456789abcdef';
            const nonce = 'abc123nonce';

            const message = walletAuthService.okxMessageToSign(address, nonce);

            expect(message.startsWith('Welcome to Xbit!')).toBe(true);
            expect(message.includes('https://xbit.com/terms-of-use')).toBe(true);
            expect(message.includes(`Wallet address: ${address}`)).toBe(true);
            expect(message.includes(`Nonce: ${nonce}`)).toBe(true);
            expect(message.endsWith(`Nonce: ${nonce}`)).toBe(true);
        });
    });

    describe('extractOkxWalletAddress', () => {
        it('should extract the wallet address from a valid OKX message', () => {
            const address = '0xABCDEF1234567890';
            const nonce = 'nonce123';
            const message = walletAuthService.okxMessageToSign(address, nonce);

            const extractedAddress = walletAuthService.extractOkxWalletAddress(message);

            expect(extractedAddress).toBe(address);
        });

        it('should throw an error if wallet address is not found', () => {
            const invalidMessage = 'Welcome to Xbit! No wallet address here.';
            expect(() => {
                walletAuthService.extractOkxWalletAddress(invalidMessage);
            }).toThrow();
        });
    });

    describe('verifySOLSignature', () => {
        it('should return true for a valid signature', () => {
            const keypair = Keypair.generate();
            const message = 'Test message';
            const messageBytes = decodeUTF8(message);
            const signature = Buffer.from(nacl.sign.detached(messageBytes, keypair.secretKey)).toString('base64');
            const isValid = walletAuthService.verifySolSignature(message, signature, keypair.publicKey.toString());
            expect(isValid).toBe(true);
        });

        it('should return false for an invalid signature', () => {
            const keypair = Keypair.generate();
            const anotherKeyPair = Keypair.generate();
            const message = 'Test message';
            const messageBytes = decodeUTF8(message);
            const signature = Buffer.from(nacl.sign.detached(messageBytes, anotherKeyPair.secretKey)).toString(
                'base64',
            );
            const isValid = walletAuthService.verifySolSignature(message, signature, keypair.publicKey.toString());
            expect(isValid).toBe(false);
        });
    });

    describe('verifyTrxSignature', () => {
        const tronWeb = new TronWeb({ fullHost: 'https://api.trongrid.io' });
        it('should return true for a valid signature', async () => {
            const account = await tronWeb.createAccount();
            const message = 'Test message';
            const signature = tronWeb.trx.signMessageV2(message, account.privateKey);
            const isValid = await walletAuthService.verifyTrxSignature(message, signature, account.address.base58);

            expect(isValid).toBe(true);
        });

        it('should return false for an invalid signature', async () => {
            const account = await tronWeb.createAccount();
            const anotherAccount = await tronWeb.createAccount();
            const message = 'Test message';
            const signature = tronWeb.trx.signMessageV2(message, anotherAccount.privateKey);
            const isValid = await walletAuthService.verifyTrxSignature(message, signature, account.address.base58);
            expect(isValid).toBe(false);
        });
    });
});
