import { <PERSON>tity, PrimaryKey, Property, ManyToOne } from '@mikro-orm/core';
import { Field, ObjectType } from '@nestjs/graphql';
import { v4 as uuidv4 } from 'uuid';
import { User } from '../../users/entities/user.entity';

@ObjectType()
@Entity({ tableName: 'user_withdrawal_whitelist_addresses' })
export class UserWithdrawalWhitelistAddress {
    @Field()
    @PrimaryKey({ type: 'uuid' })
    id: string = uuidv4();

    @Field(() => User)
    @ManyToOne(() => User)
    user: User;

    @Field()
    @Property({ type: 'varchar', length: 60 })
    address: string;

    @Field({ nullable: true })
    @Property({ type: 'varchar', length: 60, nullable: true })
    nickname?: string;

    @Field()
    @Property({ type: 'timestamptz', defaultRaw: 'now()' })
    createdAt: Date = new Date();

    @Field()
    @Property({ type: 'timestamptz', defaultRaw: 'now()', onUpdate: () => new Date() })
    updatedAt: Date = new Date();

    @Field()
    @Property({ type: 'timestamptz', nullable: true })
    deletedAt?: Date;
}
