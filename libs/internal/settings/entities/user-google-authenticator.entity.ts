import { Entity, Property, OneToOne, Index, Filter, PrimaryKey } from '@mikro-orm/core';
import { Field, ObjectType } from '@nestjs/graphql';
import { User } from '../../users/entities/user.entity';
import { UserGoogleAuthenticatorRepository } from '../repositories/user-google-authenticator.repository';
import { uuidv7 } from 'uuidv7';

@Filter({
    name: 'softDelete',
    cond: { deletedAt: null },
    default: true,
})
@ObjectType()
@Entity({ tableName: 'user_google_authenticator', repository: () => UserGoogleAuthenticatorRepository })
export class UserGoogleAuthenticator {
    @Field()
    @PrimaryKey({ type: 'uuid', defaultRaw: 'gen_random_uuid()' })
    id: string = uuidv7();

    @Field(() => User)
    @OneToOne(() => User, { unique: true })
    user: User;

    @Field()
    @Property({ type: 'varchar', length: 100 })
    secretKey: string;

    @Field({ nullable: true })
    @Property({ type: 'text', nullable: true })
    recoveryCodesHashed?: string;

    @Field()
    @Property({ type: 'boolean', default: false })
    isEnabled: boolean = false;

    @Field()
    @Property({ type: 'timestamptz', defaultRaw: 'now()' })
    createdAt: Date = new Date();

    @Field()
    @Property({ type: 'timestamptz', defaultRaw: 'now()', onUpdate: () => new Date() })
    updatedAt: Date = new Date();

    @Field()
    @Property({ type: 'timestamptz', nullable: true })
    deletedAt?: Date;
}
