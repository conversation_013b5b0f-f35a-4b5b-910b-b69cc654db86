import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ey, Property, ManyToOne, Unique } from '@mikro-orm/core';
import { Field, ObjectType } from '@nestjs/graphql';
import { v4 as uuidv4 } from 'uuid';
import { User } from '../../users/entities/user.entity';
import { NotificationType } from './notification-type.entity';

@ObjectType()
@Entity({ tableName: 'user_notification_preferences' })
@Unique({ properties: ['user', 'notificationType', 'channel'] })
export class UserNotificationPreference {
    @Field()
    @PrimaryKey({ type: 'uuid', defaultRaw: 'gen_random_uuid()' })
    id: string = uuidv4();

    @Field(() => User)
    @ManyToOne(() => User)
    user: User;

    @Field(() => NotificationType)
    @ManyToOne(() => NotificationType, { fieldName: 'notification_type_code' })
    notificationType: NotificationType;

    @Field()
    @Property({ type: 'varchar', length: 20 })
    channel: string;

    @Field()
    @Property({ type: 'boolean', default: true })
    isEnabled: boolean = true;

    @Field()
    @Property({ type: 'timestamptz', defaultRaw: 'now()' })
    createdAt: Date = new Date();

    @Field()
    @Property({ type: 'timestamptz', defaultRaw: 'now()', onUpdate: () => new Date() })
    updatedAt: Date = new Date();

    @Field()
    @Property({ type: 'timestamptz', nullable: true })
    deletedAt?: Date;
}
