import { Global, Module } from '@nestjs/common';
import { SettingsService } from './setting.service';
import { UserNotificationPreference } from './entities/user-notification-preference.entity';
import { UserGoogleAuthenticator } from './entities/user-google-authenticator.entity';
import { UserWithdrawalWhitelistAddress } from './entities/user-withdrawal-whitelist-address.entity';
import { MikroOrmModule } from '@mikro-orm/nestjs';
import { NotificationType } from './entities/notification-type.entity';

@Global()
@Module({
    providers: [SettingsService],
    exports: [SettingsService],
    imports: [
        MikroOrmModule.forFeature([
            UserNotificationPreference,
            UserWithdrawalWhitelistAddress,
            UserGoogleAuthenticator,
            NotificationType,
        ]),
    ],
})
export class SettingsModule {}
