import { EntityManager, EntityRepository } from '@mikro-orm/core';
import { UserGoogleAuthenticator } from '../entities/user-google-authenticator.entity';

export class UserGoogleAuthenticatorRepository extends EntityRepository<UserGoogleAuthenticator> {
    async findByUserId(em: EntityManager, userId: string): Promise<UserGoogleAuthenticator | null> {
        return em.findOne(UserGoogleAuthenticator, { user: { id: userId }, deletedAt: null });
    }
}
