import { ObjectType } from '@nestjs/graphql';
import { Field } from '@nestjs/graphql';
import { TwoFactorDTO } from 'libs/internal/settings/dto/user-2fa.dto';
import { UserNotificationPreferenceDTO } from 'libs/internal/settings/dto/user-notification-preference.dto';
import { UserWithdrawalWhitelistAddressDTO } from 'libs/internal/settings/dto/user-withdrawal-whitelist-address.dto';

@ObjectType()
export class UserSettingsDTO {
    @Field()
    id: string;

    @Field(() => [UserNotificationPreferenceDTO])
    notificationPreferences: UserNotificationPreferenceDTO[];

    @Field(() => [UserWithdrawalWhitelistAddressDTO])
    withdrawalWhitelistAddresses: UserWithdrawalWhitelistAddressDTO[];

    @Field(() => TwoFactorDTO, { nullable: true })
    googleAuthenticator?: TwoFactorDTO;
}
