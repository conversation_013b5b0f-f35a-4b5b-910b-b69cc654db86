import { Field, InputType, ObjectType } from '@nestjs/graphql';
import { NotificationTypeCategoryCode } from '../entities/notification-type.entity';

@ObjectType()
export class UserNotificationPreferenceDTO {
    @Field()
    id: string;

    @Field()
    userId: string;

    @Field()
    notificationTypeCode: string;

    @Field()
    channel: string;

    @Field()
    isEnabled: boolean;
}

@InputType()
export class UpdatePreferenceInput {
    @Field(() => NotificationTypeCategoryCode)
    notificationTypeCode: NotificationTypeCategoryCode;

    @Field()
    isEnabled: boolean;
}
