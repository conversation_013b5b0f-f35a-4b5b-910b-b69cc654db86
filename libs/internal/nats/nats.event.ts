export type UserLoggedInEvent = {
    realUserId: string;
    userId: string;
    userReferrerCode: string;
    isNewUser: boolean;
    fingerprint?: string;
    referrerCode?: string;
};

export type UserEVMWallet = {
    ID: string;
    UserID: string;
    WalletAddress: string;
    WalletID: string;
    WalletAccountID: string;
    createdAt: Date;
};

export type UserEVMWalletEvent = {
    userWallets: UserEVMWallet[];
};
