import { Global, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { NatsService } from './nats.service';
import { connect, ConnectionOptions, NatsConnection } from 'nats';
import { appConfig } from 'libs/configs';

@Global()
@Module({})
export class NatsModule {
    static registerAsync() {
        return {
            module: NatsModule,
            imports: [ConfigModule],
            providers: [
                {
                    provide: 'NATS_CLIENT',
                    useFactory: async () => {
                        const natUrls = appConfig.NATS_URL.split(',');
                        const user = appConfig.NATS_USER;
                        const pass = appConfig.NATS_PASS;
                        const v: ConnectionOptions = {
                            servers: natUrls,
                            user: user,
                            pass: pass,
                        };
                        const nc: NatsConnection = await connect(v);
                        return nc;
                    },
                },
                NatsService,
            ],
            exports: [NatsService],
        };
    }
}
