import { Injectable, OnModuleInit } from '@nestjs/common';
import { HyperLiquidTransactionMessageDto, ITransactionPayloadType } from './dto/hyper-liquid-transaction.dto';
import { HYPER_LIQUID_MAINNET_API_URL, HYPER_LIQUID_TESTNET_API_URL } from './constant/hyperliquid.constant';
import * as msgpack from '@msgpack/msgpack';
import { JsonRpcProvider, keccak256, Signature } from 'ethers';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';

@Injectable()
export class HyperLiquidSdkService implements OnModuleInit {
    private isMainnet = true;
    private baseUrl = '';
    // private provider: JsonRpcProvider;

    constructor(
        @InjectPinoLogger(HyperLiquidSdkService.name)
        readonly logger: PinoLogger,
    ) {}

    onModuleInit() {
        this.baseUrl = this.isMainnet ? HYPER_LIQUID_MAINNET_API_URL : HYPER_LIQUID_TESTNET_API_URL;
        // this.provider = new JsonRpcProvider(
        //     this.isMainnet ? 'https://rpc.hyperliquid.xyz/evm ' : 'https://rpc.hyperliquid-testnet.xyz/evm',
        // );
    }

    createAddAgentMessage(
        agentAddress: string,
        agentName: string,
        nonceInput?: number,
    ): HyperLiquidTransactionMessageDto {
        const nonce = nonceInput ? nonceInput : Date.now();

        const action = {
            type: 'approveAgent',
            agentAddress: agentAddress,
            agentName: agentName ?? '',
            nonce: nonce,
        };

        const data = this.buildUserTransactionData(
            action,
            [
                { name: 'hyperliquidChain', type: 'string' },
                { name: 'agentAddress', type: 'address' },
                { name: 'agentName', type: 'string' },
                { name: 'nonce', type: 'uint64' },
            ],
            'HyperliquidTransaction:ApproveAgent',
        );

        return {
            nonce,
            action,
            data,
        };
    }

    createFeeBuilderMessage(
        builder: string,
        maxFeeRate: number,
        nonceInput?: number,
    ): HyperLiquidTransactionMessageDto {
        const nonce = nonceInput ? nonceInput : Date.now();

        const action = {
            maxFeeRate: `${maxFeeRate}%`,
            builder: builder,
            nonce: nonce,
            type: 'approveBuilderFee',
        };

        const data = this.buildUserTransactionData(
            action,
            [
                { name: 'hyperliquidChain', type: 'string' },
                { name: 'maxFeeRate', type: 'string' },
                { name: 'builder', type: 'address' },
                { name: 'nonce', type: 'uint64' },
            ],
            'HyperliquidTransaction:ApproveBuilderFee',
        );

        return {
            nonce,
            action,
            data,
        };
    }

    createReferralMessage(code: string, nonceInput?: number): HyperLiquidTransactionMessageDto {
        const nonce = nonceInput ? nonceInput : Date.now();

        const action = {
            type: 'setReferrer',
            code: code,
        };

        const data = this.buildL1Action(action, null, nonce);

        return {
            nonce,
            action,
            data,
        };
    }

    buildUserTransactionData(action: object, payloadTypes: ITransactionPayloadType[], primaryType: string) {
        action['signatureChainId'] = this.isMainnet ? '0xa4b1' : '0x66eee';
        action['hyperliquidChain'] = this.isMainnet ? 'Mainnet' : 'Testnet';

        return {
            domain: {
                name: 'HyperliquidSignTransaction',
                version: '1',
                chainId: this.isMainnet ? 42161 : 421614,
                verifyingContract: '0x0000000000000000000000000000000000000000',
            },
            types: {
                [primaryType]: payloadTypes,
                // EIP712Domain: [ // Properly define and use EIP712Domain
                //     { name: 'name', type: 'string' },
                //     { name: 'version', type: 'string' },
                //     { name: 'chainId', type: 'uint256' },
                //     { name: 'verifyingContract', type: 'address' },
                // ],
            },
            primaryType: primaryType,
            message: action,
        };
    }

    async postAction(action, signature, nonce, vaultAddress?: string) {
        const payload = {
            action: action,
            nonce: nonce,
            signature: signature,
            vaultAddress: action['type'] != 'usdClassTransfer' ? vaultAddress : null,
        };

        return this.post('/exchange', payload);
    }

    async post(path: string, payload?: object) {
        const payloadReq = payload ?? {};
        const url = this.baseUrl + path;
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(payloadReq),
        });

        try {
            await this.handleException(response);
        } catch (e) {
            return { error: e.message };
        }

        try {
            return response.json();
        } catch (e) {
            return { error: `Could not parse JSON: {response.text}` };
        }
    }

    async handleException(response: Response) {
        const statusCode = response.status;
        if (statusCode < 400) return;
        if (400 <= statusCode && statusCode < 500) {
            try {
                const err = await response.json();

                if (err && err.message) {
                    throw new Error(err.message);
                }

                throw new Error('Unknown error');
            } catch (e) {
                this.logger.error(
                    { errStack: e?.stack, err: e, status: response.status },
                    'Error parsing error response',
                );
                throw new Error('Unable to decode reponse');
            }
        }

        throw new Error('Unknow error');
    }

    extractSignatureComponents(signature: string): { r: string; s: string; v: number } {
        const { r, s, v } = Signature.from(signature);
        return { r, s, v };
    }

    buildL1Action(action, activePool, nonce) {
        const hash = this.actionHash(action, activePool, nonce);
        const phantomAgent = this.constructPhantomAgent(hash);
        return {
            domain: {
                chainId: 1337,
                name: 'Exchange',
                verifyingContract: '0x0000000000000000000000000000000000000000',
                version: '1',
            },
            types: {
                Agent: [
                    { name: 'source', type: 'string' },
                    { name: 'connectionId', type: 'bytes32' },
                ],
                // "EIP712Domain": [
                //     {"name": "name", "type": "string"},
                //     {"name": "version", "type": "string"},
                //     {"name": "chainId", "type": "uint256"},
                //     {"name": "verifyingContract", "type": "address"},
                // ],
            },
            primaryType: 'Agent',
            message: phantomAgent,
        };
    }

    actionHash(action: object, vaultAddress: string | null, nonce: number): string {
        let data = msgpack.encode(action);

        const nonceBuffer = Buffer.alloc(8);
        nonceBuffer.writeBigUInt64BE(BigInt(nonce));
        data = Buffer.concat([data, nonceBuffer]);

        if (!vaultAddress) {
            data = Buffer.concat([data, Buffer.from([0x00])]);
        } else {
            data = Buffer.concat([data, Buffer.from([0x01]), this.addressToBytes(vaultAddress)]);
        }

        return keccak256(data);
    }

    addressToBytes(address: string): Uint8Array {
        const normalizedAddress = address.startsWith('0x') ? address.slice(2) : address;
        return Uint8Array.from(Buffer.from(normalizedAddress, 'hex'));
    }

    constructPhantomAgent(hash: string): { source: string; connectionId: string } {
        return { source: this.isMainnet ? 'a' : 'b', connectionId: hash };
    }

    // getBalance(address: string): Promise<bigint> {
    //     return this.provider.getBalance(address);
    // }
}
