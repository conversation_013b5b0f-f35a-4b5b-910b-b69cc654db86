import { Module } from '@nestjs/common';
import { HyperLiquidService } from './hyper-liquid.service';
import { WalletModule } from '../wallet/wallet.module';
import { UsersModule } from '../users/users.module';
import { HyperLiquidSdkService } from './hyper-liquid-sdk.service';
import { VaultManagementModule } from '@libs/internal/vault-management';
import { TurnkeyModule } from '@lib/internal/turnkey';

@Module({
    imports: [WalletModule, UsersModule, VaultManagementModule, TurnkeyModule],
    providers: [HyperLiquidService, HyperLiquidSdkService],
    exports: [HyperLiquidService, HyperLiquidSdkService],
})
export class HyperLiquidModule {}
