import { Args, Field, InputType, ObjectType } from '@nestjs/graphql';
import { Input } from 'telegraf';

@ObjectType()
export class PerpetualStatusDTO {
    @Field(() => Boolean)
    approvedAgent: boolean;

    @Field(() => Boolean)
    setReferral: boolean;

    @Field(() => Boolean)
    setFeeBuilder: boolean;

    @Field(() => String, { nullable: true })
    agent: string | null;

    @Field(() => String)
    agentName: string;

    @Field(() => String)
    feeBuilderAddress: string;

    @Field(() => Number)
    feeBuilderPercent: number;

    @Field(() => String)
    referralCode: string;
}

@InputType()
export class InputPerpetualStatusDTO {
    @Field(() => Number, { nullable: true })
    agentExpiredAt: number;

    @Field(() => Boolean)
    setReferral: boolean;

    @Field(() => Boolean)
    setFeeBuilder: boolean;

    @Field(() => String)
    feeBuilderAddress: string;

    @Field(() => Number)
    feeBuilderPercent: number;

    @Field(() => String)
    referralCode: string;
}
