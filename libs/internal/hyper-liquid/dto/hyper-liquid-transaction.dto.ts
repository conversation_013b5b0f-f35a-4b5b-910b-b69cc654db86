import { InputType, Field, Float, Int, ObjectType } from '@nestjs/graphql';
import { Input } from 'telegraf';

export interface ITransactionPayloadType {
    name: string;
    type: string;
}

export class HyperLiquidTransactionMessageDto {
    nonce: number;
    action: {
        type: string;
        order?: any;
        code?: string;
        maxFeeRate?: string;
        builder?: string;
        nonce?: number;
        grouping?: string;
    };
    data: {
        domain: {
            chainId: number;
            name: string;
            verifyingContract: string;
            version: string;
        };
        types: {
            [x: string]: ITransactionPayloadType[];
        };
        primaryType: string;
        message: Record<string, any>;
    };
}

@InputType()
export class LimitInput {
    @Field(() => String)
    tif: 'Alo' | 'Ioc' | 'Gtc';
}

@InputType()
export class TriggerInput {
    @Field(() => Boolean)
    isMarket: boolean;

    @Field(() => String)
    triggerPx: string;

    @Field(() => String)
    tpsl: 'tp' | 'sl';
}

@InputType()
export class OrderTypeInput {
    @Field(() => LimitInput, { nullable: true })
    limit?: LimitInput;

    @Field(() => TriggerInput, { nullable: true })
    trigger?: TriggerInput;
}

@InputType()
export class OrderInput {
    @Field(() => Float)
    a: number;

    @Field()
    b: boolean;

    @Field()
    p: string;

    @Field()
    s: string;

    @Field()
    r: boolean;

    @Field(() => OrderTypeInput)
    t: OrderTypeInput;

    @Field({ nullable: true })
    c?: string;
}

@InputType()
export class BuilderInput {
    @Field()
    b: string;

    @Field(() => Float)
    f: number;
}

@InputType()
export class OrderRequestInput {
    @Field()
    type: string;

    @Field(() => [OrderInput])
    orders: OrderInput[];

    @Field({ nullable: true })
    grouping?: string;

    @Field(() => BuilderInput, { nullable: true })
    builder?: BuilderInput;
}

@InputType()
export class InputSignCreateOrderDTO {
    @Field(() => OrderRequestInput)
    action: OrderRequestInput;

    @Field(() => Float)
    nonce: number;

    @Field({ nullable: true })
    vaultAddress?: string;
}

@ObjectType()
export class SignatureType {
    @Field(() => String)
    r: string;
    @Field(() => String)
    s: string;
    @Field(() => Int)
    v: number;
}

@ObjectType()
export class SignedCreateOrderDTO {
    @Field(() => SignatureType)
    signature: SignatureType;

    @Field(() => String)
    userId: string;
}

@InputType()
export class CancelInput {
    @Field(() => Int)
    a: number;

    @Field(() => Float)
    o: number;
}

@InputType()
export class CancelActionInput {
    @Field()
    type: string;

    @Field(() => [CancelInput])
    cancels: CancelInput[];
}

@InputType()
export class InputSignCancelOrderDTO {
    @Field(() => CancelActionInput)
    action: CancelActionInput;

    @Field(() => Float)
    nonce: number;

    @Field({ nullable: true })
    vaultAddress?: string;
}

@ObjectType()
export class SignedCancelOrderDTO {
    @Field(() => SignatureType)
    signature: SignatureType;

    @Field(() => String)
    userId: string;
}

@InputType()
export class InputSignUpdateLeverageDTO {
    @Field(() => UpdateLeverageActionInput)
    action: CancelActionInput;

    @Field(() => Float)
    nonce: number;

    @Field({ nullable: true })
    vaultAddress?: string;
}

@InputType()
export class UpdateLeverageActionInput {
    @Field(() => String)
    type: string;

    @Field(() => Int)
    asset: number;

    @Field(() => Boolean)
    isCross: boolean;

    @Field(() => Int)
    leverage: number;
}

@ObjectType()
export class SignUpdateLeverageResponseDTO {
    @Field(() => SignatureType)
    signature: SignatureType;

    @Field(() => String)
    userId: string;
}

@InputType()
export class InputSignApproveAgentDTO {
    @Field(() => String, {
        description: 'The address of the agent wallet that needs to approve. BE uses to validate transaction',
    })
    agentAddress: string;

    @Field(() => Float, {
        description: 'The nonce of transaction that was built from FE. BE uses to validate transaction',
    })
    nonce: number;

    @Field(() => String, {
        description: 'The name of the agent wallet that needs to approve. BE uses to validate transaction',
    })
    agentName: string;

    @Field(() => String, {
        description: 'The ID of the activity that submitted to Turnkey from FE thru signRawPayload methods',
    })
    activityId: string;
}

@InputType({ description: 'Note that builder address needs to matched with config in BE' })
export class InputSignApproveFeeBuilderDTO {
    @Field(() => Float, {
        description: 'The nonce of transaction that was built from FE. BE uses to validate transaction',
    })
    nonce: number;

    @Field(() => String, {
        description: 'The ID of the activity that submitted to Turnkey from FE thru signRawPayload methods',
    })
    activityId: string;
}

@InputType({ description: 'Note that referral code needs to matched with config in BE' })
export class InputSignApproveReferralDTO {
    @Field(() => Float, {
        description: 'The nonce of transaction that was built from FE. BE uses to validate transaction',
    })
    nonce: number;

    @Field(() => String, {
        description: 'The ID of the activity that submitted to Turnkey from FE thru signRawPayload methods',
    })
    activityId: string;
}

@ObjectType({ description: 'Signature of Hyperliquid transaction that uses to submit to Hyperliquid API' })
export class SignatureDTO {
    @Field(() => SignatureType)
    signature: SignatureType;

    @Field(() => String)
    userId: string;
}
