import * as jwt from 'jsonwebtoken';

export function extractJwtPayload(token: string): Record<string, any> | null {
    try {
        const payload = jwt.decode(token);
        return typeof payload === 'object' ? payload : null;
    } catch (error) {
        console.error('Error decoding JWT:', error);
        return null;
    }
}

export const getDataCheckString = (data: Record<string, any>): string => {
    return Object.keys(data)
        .sort()
        .map((key) => `${key}=${data[key]}`)
        .join('\n');
};
