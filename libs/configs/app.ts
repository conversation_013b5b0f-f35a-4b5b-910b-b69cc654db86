import { getEnv } from './env';

export const appConfig = {
    INTERNAL_GRPC_PORT: parseInt(getEnv('INTERNAL_GRPC_PORT', '5001')),

    STAGE: getEnv('STAGE')?.toLowerCase(),
    JWT_SECRET: getEnv('JWT_SECRET'),
    JWT_REFRESH_SECRET: getEnv('JWT_REFRESH_SECRET'),
    TELEGRAM_OIDC_SECRET: getEnv('JWT_OIDC'),

    TELEGRAM_BOT_DOMAIN: getEnv('TELEGRAM_BOT_DOMAIN'),
    TELEGRAM_BOT_WEBHOOK: getEnv('TELEGRAM_BOT_WEBHOOK'),
    TELEGRAM_BOT_AUTH_TOKEN: getEnv('TELEGRAM_BOT_AUTH_TOKEN'),
    TELEGRAM_BOT_AUTH_NAME: 'auth',
    TELEGRAM_LOGIN_URL: getEnv('TELEGRAM_LOGIN_URL'),

    ENCRYPT_PUBLIC_KEY: getEnv('ENCRYPT_PUBLIC_KEY').replace(/\\n/g, '\n'),

    CORS_ORIGINS: getEnv('XBIT_WEB_DOMAINS', '*').split(',') || [],
    CORS_MAX_AGE_HOUR: parseInt(getEnv('CORS_MAX_AGE_HOUR', '24')),
    API_ORIGINS: getEnv('XBIT_API_DOMAIN', '*').split(',') || [],

    GRAPHQL_BASIC_AUTH_USER: getEnv('GRAPHQL_BASIC_AUTH_USER', '***'),
    GRAPHQL_BASIC_AUTH_PASS: getEnv('GRAPHQL_BASIC_AUTH_PASS', '***'),

    WALLET_SERVICE_HOST: getEnv('WALLET_SERVICE_HOST'),
    WALLET_SERVICE_PORT: getEnv('WALLET_SERVICE_PORT'),
    WALLET_SERVICE_APIKEY: getEnv('WALLET_SERVICE_APIKEY'),

    INFURA_RPC_URL: getEnv('INFURA_RPC_URL'),
    TRON_GRID_API_KEY: getEnv('TRON_GRID_API_KEY'),

    RETRY_THRESHOLD: Number(getEnv('RETRY_THRESHOLD', '5')),

    LANDING_PAGE_URL: getEnv('LANDING_PAGE_URL'),

    REFERRAL_ADDRESS: getEnv('REFERRAL_ADDRESS'),
    CHANNELS: '',

    WEBSTITE_ADDRESS: getEnv('WEBSTITE_ADDRESS'),
    TOKEN_DETAILS: getEnv('TOKEN_DETAILS'),
    MEME_BASE_URL: getEnv('MEME_BASE_URL'),
    TRANDING_BASE_URL: getEnv('TRANDING_BASE_URL'),

    ACCESS_TOKEN_EXPIRES_IN: getEnv('ACCESS_TOKEN_EXPIRES_IN', '1d'),
    REFRESH_TOKEN_EXPIRES_IN: getEnv('REFRESH_TOKEN_EXPIRES_IN', '30d'),

    NATS_URL: getEnv('NATS_URL', ''),
    NATS_AUTH_TOKEN: getEnv('NATS_AUTH_TOKEN', ''),
    NATS_USER: getEnv('NATS_USER', ''),
    NATS_PASS: getEnv('NATS_PASS', ''),

    TURNKEY_API_BASE_URL: getEnv('TURNKEY_API_BASE_URL'),
    TURNKEY_API_PRIVATE_KEY: getEnv('TURNKEY_API_PRIVATE_KEY'),
    TURNKEY_API_PUBLIC_KEY: getEnv('TURNKEY_API_PUBLIC_KEY'),
    TURNKEY_ORGANIZATION_ID: getEnv('TURNKEY_ORGANIZATION_ID'),

    TURNKEY_FAKE_PUBLIC_KEY: getEnv('TURNKEY_FAKE_PUBLIC_KEY'),
    TURNKEY_FAKE_PRIVATE_KEY: getEnv('TURNKEY_FAKE_PRIVATE_KEY'),

    // Google OAuth Configuration
    GOOGLE_CLIENT_ID: getEnv('GOOGLE_CLIENT_ID', ''),
    GOOGLE_CLIENT_SECRET: getEnv('GOOGLE_CLIENT_SECRET', ''),
    GOOGLE_CALLBACK_URL: getEnv('GOOGLE_CALLBACK_URL', ''),
    GOOGLE_OIDC_SECRET: getEnv('GOOGLE_OIDC', ''),

    SENTRY_DSN: getEnv('SENTRY_DSN_USER', ''),

    APPLE_CLIENT_ID: getEnv('APPLE_CLIENT_ID', ''),

    NATS_USER_STREAM: getEnv('NATS_USER_STREAM', ''),
    NATS_SUBJECT_WALLET_CREATE: getEnv('NATS_SUBJECT_WALLET_CREATE'),

    NATS_DEX_USER_STREAM: getEnv('NATS_DEX_USER_STREAM', ''),
    NATS_SUBJECT_DEX_WALLET_CREATE: getEnv('NATS_SUBJECT_DEX_WALLET_CREATE', ''),
};
