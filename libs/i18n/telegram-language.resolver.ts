// src/i18n/telegram-language.resolver.ts
import { Injectable, OnModuleInit } from '@nestjs/common';
import { I18nResolver } from 'nestjs-i18n';
import { ExecutionContext } from '@nestjs/common';

@Injectable()
export class TelegramLanguageResolver implements I18nResolver {
    resolve(context: ExecutionContext): string | undefined {
        const ctx = context.getArgByIndex(0); // Telegraf Context
        return ctx?.from?.language_code || 'vi'; // fallback
    }
}
