import { I18nService } from 'nestjs-i18n';
import { INestApplicationContext } from '@nestjs/common';
import * as path from 'path';
import * as fs from 'fs';

let appContext: INestApplicationContext;

export function setAppContext(app: INestApplicationContext) {
    appContext = app;
}

export function translate(key: string, lang = 'en', args?: Record<string, any>): string {
    if (!appContext) {
        throw new Error('App context chưa được khởi tạo. Gọi setAppContext(app) trong main.ts');
    }
    // const i18n = appContext.get(I18nService);
    // if (isLanguageFileExists(lang)) return i18n.t(`validation.${key}`, { lang, args });
    else return key;
}
export function isLanguageFileExists(lang: string): boolean {
    const basePath = path.join(__dirname, '..', '..', 'libs', 'i18n', 'locales');
    const folderPath = path.join(basePath, lang);
    const filePath = path.join(folderPath, 'validation.json');

    try {
        if (!fs.existsSync(folderPath) || !fs.statSync(folderPath).isDirectory()) {
            return false;
        }
        if (!fs.existsSync(filePath) || !fs.statSync(filePath).isFile()) {
            return false;
        }
        return true;
    } catch {
        return false;
    }
}
