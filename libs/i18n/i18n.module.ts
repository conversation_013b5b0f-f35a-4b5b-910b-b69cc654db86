// src/i18n/i18n.module.ts
import { Module } from '@nestjs/common';
import {
    I18nModule,
    QueryResolver,
    AcceptLanguageResolver,
    HeaderResolver,
    GraphQLWebsocketResolver,
} from 'nestjs-i18n';
import { join } from 'path';
import { TelegramLanguageResolver } from './telegram-language.resolver';

@Module({
    imports: [
        I18nModule.forRoot({
            fallbackLanguage: 'en',
            loaderOptions: {
                path: join(__dirname, '/locales/'),
                watch: true,
            },
            resolvers: [
                new GraphQLWebsocketResolver(),
                // Cho GraphQL
                { use: QueryResolver, options: ['lang', 'locale'] },
                // Cho Telegram: bạn sẽ set ctx.i18nLang thủ công
                { use: HeaderResolver, options: ['x-custom-lang'] },
                AcceptLanguageResolver,
                // { use: QueryResolver, options: ['lang'] },
                // new HeaderResolver(['x-lang']),
                // new TelegramLanguageResolver(),
            ],
        }),
    ],
    providers: [TelegramLanguageResolver],
})
export class AppI18nModule {}
