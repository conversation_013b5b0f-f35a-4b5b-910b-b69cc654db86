import { Injectable } from '@nestjs/common';
import { PinoLogger } from 'nestjs-pino';

@Injectable()
export class LoggerService {
    private readonly logger = new PinoLogger({
        renameContext: LoggerService.name,
    });

    log(message: any, ...optionalParams: any[]) {
        this.logger.info(optionalParams, message);
    }

    error(message: any, ...optionalParams: any[]) {
        this.logger.error(optionalParams, message);
    }

    warn(message: any, ...optionalParams: any[]) {
        this.logger.warn(optionalParams, message);
    }

    debug?(message: any, ...optionalParams: any[]) {
        this.logger.debug(optionalParams, message);
    }

    verbose?(message: any, ...optionalParams: any[]) {
        this.logger.trace(optionalParams, message);
    }

    fatal?(message: any, ...optionalParams: any[]) {
        this.logger.fatal(optionalParams, message);
    }
}
