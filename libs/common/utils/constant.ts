export enum PAIR_WINDOW_FILTER {
    WINDOW_1_MINUTE = '1_MINUTE',
    WINDOW_5_MINUTE = '5_MINUTE',
    WINDOW_1_HOUR = '1_HOUR',
    WINDOW_6_HOUR = '6_HOUR',
    WINDOW_1_DAY = '1_DAY',
}

export enum SUPPORT_DEX {
    PUMP = 'pump',
    MOONSHOT = 'moonshoot',
    RAYDIUM = 'raydium',
}

export const SUPPORT_DEX_FACTORY = {
    [SUPPORT_DEX.PUMP]: ['6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P'],
    [SUPPORT_DEX.MOONSHOT]: ['MoonCVVNZFSYkqNXP6bxHLPL6QQJiMagDL3qcqUQTrG'],
    [SUPPORT_DEX.RAYDIUM]: [
        '675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8',
        'CPMMoo8L3F4NbTegBCKVNunggL7H1ZpdTHKxQB5qKP1C',
    ],
};

export enum SORT_TYPE {
    DESC = 'DESC',
    ASC = 'ASC',
}
