export function parseSortByString(sortBy: string): Record<string, 1 | -1> {
    const sortFields = sortBy
        .split(' ')
        .map((field) => field.trim())
        .filter(Boolean);
    const sortObject: Record<string, 1 | -1> = {};

    for (const field of sortFields) {
        if (field.startsWith('-')) {
            sortObject[field.substring(1)] = -1;
        } else {
            sortObject[field] = 1;
        }
    }

    return sortObject;
}
