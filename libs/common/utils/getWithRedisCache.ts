import Redis from 'ioredis';
import { Mutex } from 'async-mutex';
import { LRUCache } from 'lru-cache';
import * as snappy from 'snappy';
const mutexes = new LRUCache<string, Mutex>({
    max: 1000000, // number of concurrent lock keys
    ttl: 1000 * 60 * 5,
});

const MIN_COMPRESS_SIZE = 512;
export async function getWithRedisCache<T>(
    redis: Redis,
    key: string,
    ttlSeconds: number,
    fetchFunction: () => Promise<T>,
): Promise<T> {
    const cached = await redis.getBuffer(key);
    if (cached) {
        const isCompressed = cached[0] === 49;
        const payload = cached.subarray(1);

        const decompressed = isCompressed ? await snappy.uncompress(payload) : payload;
        return JSON.parse(decompressed.toString()) as T;
    }

    let mutex = mutexes.get(key);
    if (!mutex) {
        mutex = new Mutex();
        mutexes.set(key, mutex);
    }

    return await mutex.runExclusive(async () => {
        const recheck = await redis.getBuffer(key);
        if (recheck) {
            // if first character is 1 ('1' = 49 in ASCII) , data is compressed
            const isCompressed = recheck[0] === 49;
            const payload = recheck.subarray(1);

            const decompressed = isCompressed ? await snappy.uncompress(payload) : payload;
            return JSON.parse(decompressed.toString()) as T;
        }

        const data = await fetchFunction();
        const serialized = Buffer.from(JSON.stringify(data));

        let finalBuffer: Buffer;

        if (serialized.length > MIN_COMPRESS_SIZE) {
            const compressed = await snappy.compress(serialized);
            finalBuffer = Buffer.concat([Buffer.from('1'), compressed]);
        } else {
            finalBuffer = Buffer.concat([Buffer.from('0'), serialized]);
        }

        await redis.set(key, finalBuffer, 'EX', ttlSeconds);
        return data;
    });
}
