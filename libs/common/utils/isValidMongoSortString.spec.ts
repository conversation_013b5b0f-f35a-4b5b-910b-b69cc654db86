import { isValidMongoSortString } from './is-valid-mongo-sort-string';

describe('isValidMongoSortString', () => {
    it('should return true for valid sort string with ascending and descending fields', () => {
        expect(isValidMongoSortString('title -created_at')).toBe(true);
        expect(isValidMongoSortString('-updatedAt name')).toBe(true);
    });

    it('should return true for a single valid field', () => {
        expect(isValidMongoSortString('title')).toBe(true);
        expect(isValidMongoSortString('-title')).toBe(true);
    });

    it('should return false for fields starting with number', () => {
        expect(isValidMongoSortString('123title')).toBe(false);
        expect(isValidMongoSortString('-1name')).toBe(false);
    });

    it('should return false for fields with invalid characters', () => {
        expect(isValidMongoSortString('title@name')).toBe(false);
        expect(isValidMongoSortString('created#at')).toBe(false);
        expect(isValidMongoSortString('na!me')).toBe(false);
    });

    it('should return false for empty string', () => {
        expect(isValidMongoSortString('')).toBe(false);
    });

    it('should handle extra whitespace and still validate correctly', () => {
        expect(isValidMongoSortString('  -created_at    title ')).toBe(true);
    });

    it('should return false for invalid type (non-string)', () => {
        // @ts-expect-error testing non-string input
        expect(isValidMongoSortString(null)).toBe(false);
        // @ts-expect-error
        expect(isValidMongoSortString(undefined)).toBe(false);
        // @ts-expect-error
        expect(isValidMongoSortString(123)).toBe(false);
    });
});
