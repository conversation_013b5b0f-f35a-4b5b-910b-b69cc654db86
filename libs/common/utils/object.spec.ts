import { removeNullAndUndefined } from './object';

describe('removeNullAndUndefined', () => {
    test('removes null and undefined values', () => {
        const input = {
            name: '<PERSON>',
            age: null,
            email: '<EMAIL>',
            address: undefined,
            country: 'USA',
        };
        const expectedOutput = {
            name: '<PERSON>',
            email: '<EMAIL>',
            country: 'USA',
        };
        expect(removeNullAndUndefined(input)).toEqual(expectedOutput);
    });

    test('returns empty object when all values are null or undefined', () => {
        const input = {
            key1: null,
            key2: undefined,
            key3: null,
        };
        expect(removeNullAndUndefined(input)).toEqual({});
    });

    test('returns the same object if no null or undefined values exist', () => {
        const input = {
            id: 1,
            name: 'Alice',
            isActive: true,
        };
        expect(removeNullAndUndefined(input)).toEqual(input);
    });

    test('handles empty object', () => {
        const input = {};
        expect(removeNullAndUndefined(input)).toEqual({});
    });

    test('does not remove falsy values like 0, false, or empty string', () => {
        const input = {
            zero: 0,
            emptyString: '',
            isFalse: false,
            nullValue: null,
            undefinedValue: undefined,
        };
        const expectedOutput = {
            zero: 0,
            emptyString: '',
            isFalse: false,
        };
        expect(removeNullAndUndefined(input)).toEqual(expectedOutput);
    });

    test('removes null and undefined values from nested objects (not deeply)', () => {
        const input = {
            name: 'Nested',
            nested: {
                key1: 'value1',
                key2: null,
                key3: undefined,
            },
        };
        const expectedOutput = {
            name: 'Nested',
            nested: {
                key1: 'value1',
                key2: null,
                key3: undefined,
            },
        };
        expect(removeNullAndUndefined(input)).toEqual(expectedOutput);
    });
});
