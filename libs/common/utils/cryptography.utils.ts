import { generateKeyPairSync, publicEncrypt, privateDecrypt } from 'crypto';

/**
 * Generate RSA Key Pair (2048-bit)
 */
export function generateRSAKeys(): { publicKey: string; privateKey: string } {
    const { publicKey, privateKey } = generateKeyPairSync('rsa', {
        modulusLength: 2048, // Key size
        publicKeyEncoding: {
            type: 'spki',
            format: 'pem',
        },
        privateKeyEncoding: {
            type: 'pkcs8',
            format: 'pem',
        },
    });

    return { publicKey, privateKey };
}

/**
 * Encrypt data using RSA public key
 * @param data Plain text data to encrypt
 * @param publicKey Public key in PEM format
 * @returns Encrypted data (Base64 encoded)
 */
export function encryptRSA(data: string, publicKey: string): string {
    const buffer = Buffer.from(data, 'utf8');
    const encrypted = publicEncrypt(publicKey, buffer);
    return encrypted.toString('hex');
}

/**
 * Decrypt data using RSA private key
 * @param encryptedData Base64 encoded encrypted string
 * @param privateKey Private key in PEM format
 * @returns Decrypted plain text data
 */
export function decryptRSA(encryptedData: string, privateKey: string): string {
    const buffer = Buffer.from(encryptedData, 'hex');
    const decrypted = privateDecrypt(privateKey, buffer);
    return decrypted.toString('utf8');
}
