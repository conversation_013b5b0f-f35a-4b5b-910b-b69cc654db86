import { generateRSAKeys, encryptRSA, decryptRSA } from './cryptography.utils';

describe('cryptography.utils.ts', () => {
    let keys: { publicKey: string; privateKey: string };
    let plaintext: string;
    let encryptedData: string;

    beforeAll(() => {
        // Generate RSA keys before running tests
        keys = generateRSAKeys();
        plaintext = 'Hello, World!';
    });

    test('should generate RSA key pair', () => {
        expect(keys.publicKey).toBeDefined();
        expect(keys.privateKey).toBeDefined();
        expect(keys.publicKey).toContain('BEGIN PUBLIC KEY');
        expect(keys.privateKey).toContain('BEGIN PRIVATE KEY');
    });

    test('should encrypt data correctly', () => {
        encryptedData = encryptRSA(plaintext, keys.publicKey);
        expect(encryptedData).toBeDefined();
        expect(encryptedData).not.toBe(plaintext);
    });

    test('should decrypt data correctly', () => {
        const decryptedData = decryptRSA(encryptedData, keys.privateKey);
        expect(decryptedData).toBe(plaintext);
    });

    test('should fail to decrypt with wrong key', () => {
        const otherKeys = generateRSAKeys();
        expect(() => decryptRSA(encryptedData, otherKeys.privateKey)).toThrow();
    });
});
