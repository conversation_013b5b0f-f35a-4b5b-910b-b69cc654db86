import { parseSortByString } from './parse-mongosort-by-string';

describe('parseSortByString', () => {
    it('should return ascending sort for single field', () => {
        expect(parseSortByString('name')).toEqual({ name: 1 });
    });

    it('should return descending sort for single field', () => {
        expect(parseSortByString('-createdAt')).toEqual({ createdAt: -1 });
    });

    it('should handle multiple fields with mixed order', () => {
        expect(parseSortByString('name -createdAt')).toEqual({
            name: 1,
            createdAt: -1,
        });
    });

    it('should trim whitespace around fields', () => {
        expect(parseSortByString('  name   -createdAt ')).toEqual({
            name: 1,
            createdAt: -1,
        });
    });

    it('should ignore empty fields', () => {
        expect(parseSortByString(' name   -createdAt')).toEqual({
            name: 1,
            createdAt: -1,
        });
    });

    it('should return empty object for empty input', () => {
        expect(parseSortByString('')).toEqual({});
    });

    it('should return empty object for only commas or spaces', () => {
        expect(parseSortByString('    ')).toEqual({});
    });
});
