export interface IApiError {
    code: string;
    message: string;
}

export class ApiError extends <PERSON><PERSON>r {
    public code: string;
    public message: string;

    constructor(error: IApiError) {
        super(`Error ${error.code}: ${error.message}`);
        this.code = error.code;
        this.message = error.message;
    }
}

export class ServiceError extends Error {
    constructor(url: string, statusCode: number, responseText: any) {
        console.error(`Call API ${url} error. Response code: ${statusCode}.
      Response text: ${JSON.stringify(responseText)}, status code: ${statusCode}.`);
        super('INTERNAL_SERVER_ERROR');
    }
}
