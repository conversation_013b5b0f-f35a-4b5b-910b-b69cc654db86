import { Injectable, NestMiddleware } from '@nestjs/common';
import { NextFunction, Request, Response } from 'express';
import { MikroORM, RequestContext } from '@mikro-orm/core';

// @Injectable()
// export class GraphQLRequestContextMiddleware implements NestMiddleware {
//     constructor(private readonly orm: MikroORM) {}
//
//     use(req: Request, res: Response, next: NextFunction) {
//         RequestContext.create(this.orm.em, next);
//     }
// }
