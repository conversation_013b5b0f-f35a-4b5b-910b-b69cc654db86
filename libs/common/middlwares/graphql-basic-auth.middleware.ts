import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { appConfig } from '../../configs';

@Injectable()
export class GraphQLPlaygroundBasicAuthMiddleware implements NestMiddleware {
    use(req: Request, res: Response, next: NextFunction) {
        const isPlaygroundRequest = req.method === 'GET';
        if (!isPlaygroundRequest) {
            return next();
        }

        const auth = req.headers.authorization;

        if (!auth || !auth.startsWith('Basic ')) {
            res.setHeader('WWW-Authenticate', 'Basic realm="GraphQL Protected"');
            return res.status(401).end();
        }

        const credentials = Buffer.from(auth.split(' ')[1], 'base64').toString();
        const [username, password] = credentials.split(':');

        if (username !== appConfig.GRAPHQL_BASIC_AUTH_USER || password !== appConfig.GRAPHQL_BASIC_AUTH_PASS) {
            res.setHeader('WWW-Authenticate', 'Basic realm="GraphQL Protected"');
            return res.status(401).end();
        }

        next();
    }
}
