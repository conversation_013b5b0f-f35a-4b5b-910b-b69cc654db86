import { Catch, ArgumentsHost, Lo<PERSON>, HttpException } from '@nestjs/common';
import { GqlExceptionFilter, GqlArgumentsHost } from '@nestjs/graphql';
import { GraphQLError } from 'graphql';
import { ApiError } from '../api-errors';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';

@Catch()
export class GraphQLExceptionFilter implements GqlExceptionFilter {
    constructor(
        @InjectPinoLogger(GraphQLExceptionFilter.name)
        private readonly logger: PinoLogger,
    ) {}

    catch(exception: any, host: ArgumentsHost) {
        const gqlHost = GqlArgumentsHost.create(host);

        // Check if it's a known exception
        if (exception instanceof GraphQLError) {
            return exception;
        }

        if (exception instanceof HttpException) {
            return exception;
        }

        if (exception instanceof ApiError) {
            return new GraphQLError(exception.message, {
                extensions: {
                    code: exception.code,
                },
            });
        }

        this.logger.error(exception);
        // Convert NestJS exception to GraphQL error
        return new GraphQLError('Internal server error', {
            extensions: {
                code: exception.code || 'INTERNAL_SERVER_ERROR',
            },
        });
    }
}
