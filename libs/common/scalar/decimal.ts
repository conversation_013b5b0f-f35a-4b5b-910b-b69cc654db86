import { CustomScalar, <PERSON>alar } from '@nestjs/graphql';
import Decimal from 'decimal.js';

@Scalar('DecimalScalar')
export class DecimalScalar implements CustomScalar<string, Decimal> {
    description = 'scalar for decimal value';

    parseValue(value: string): Decimal {
        return new Decimal(value);
    }

    serialize(value: any): string {
        try {
            const decimalValue = new Decimal(value.toString());
            if (decimalValue.isZero()) {
                return '0';
            }
            return decimalValue.toString();
        } catch (_) {
            return '0';
        }
    }

    parseLiteral(ast: any): Decimal {
        if (ast.kind === 'StringValue' || ast.kind === 'IntValue' || ast.kind === 'FloatValue') {
            return new Decimal(ast.value);
        }
        //@ts-ignore
        return null;
    }
}
