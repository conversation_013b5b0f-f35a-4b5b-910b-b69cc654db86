import { DynamicModule, Global, Module } from '@nestjs/common';
import { RedisService } from './redis.service';
import Redis from 'ioredis';
import { ConfigModule, ConfigService } from '@nestjs/config';

export interface RedisModuleOptions {
    host?: string;
    port?: number;
    password?: string;
    db?: number;
    url?: string;
    keyPrefix?: string;
}

@Global()
@Module({})
export class RedisModule {
    static register(options: RedisModuleOptions = {}): DynamicModule {
        return {
            module: RedisModule,
            providers: [
                {
                    provide: 'REDIS_CLUSTER',
                    useFactory: (configService: ConfigService) => {
                        const REDIS_HOST = configService.get<string>('REDIS_CLUSTER_HOST') || 'localhost';
                        const REDIS_PORT = parseInt(configService.get<string>('REDIS_CLUSTER_PORT') || '6379', 10);
                        const REDIS_CLUSTER_ENABLE_SSL =
                            configService.get<string>('REDIS_CLUSTER_ENABLE_SSL') === 'true';
                        const REDIS_CLUSTER_PRIMARY_PASS =
                            configService.get<string>('REDIS_CLUSTER_PRIMARY_PASS') || '';
                        const REDIS_CLUSTER_SECONDARY_PASS =
                            configService.get<string>('REDIS_CLUSTER_SECONDARY_PASS') || '';

                        return new Redis.Cluster(
                            [
                                { host: REDIS_HOST, port: REDIS_PORT, password: REDIS_CLUSTER_PRIMARY_PASS } as any,
                                { host: REDIS_HOST, port: REDIS_PORT, password: REDIS_CLUSTER_SECONDARY_PASS } as any,
                            ],
                            {
                                redisOptions: {
                                    password: REDIS_CLUSTER_PRIMARY_PASS || REDIS_CLUSTER_SECONDARY_PASS,
                                    tls: REDIS_CLUSTER_ENABLE_SSL
                                        ? {
                                              rejectUnauthorized: false,
                                          }
                                        : undefined,
                                },
                            },
                        );
                    },
                    inject: [ConfigService],
                },
                RedisService,
            ],
            exports: [RedisService],
        };
    }

    static registerAsync(): DynamicModule {
        return {
            module: RedisModule,
            imports: [ConfigModule],
            providers: [
                {
                    provide: 'REDIS_CLUSTER',
                    useFactory: (configService: ConfigService) => {
                        const REDIS_HOST = configService.get<string>('REDIS_CLUSTER_HOST') || 'localhost';
                        const REDIS_PORT = parseInt(configService.get<string>('REDIS_CLUSTER_PORT') || '6379', 10);
                        const REDIS_CLUSTER_ENABLE_SSL =
                            configService.get<string>('REDIS_CLUSTER_ENABLE_SSL') === 'true';
                        const REDIS_CLUSTER_PRIMARY_PASS =
                            configService.get<string>('REDIS_CLUSTER_PRIMARY_PASS') || '';
                        const REDIS_CLUSTER_SECONDARY_PASS =
                            configService.get<string>('REDIS_CLUSTER_SECONDARY_PASS') || '';

                        return new Redis.Cluster(
                            [
                                { host: REDIS_HOST, port: REDIS_PORT, password: REDIS_CLUSTER_PRIMARY_PASS } as any,
                                { host: REDIS_HOST, port: REDIS_PORT, password: REDIS_CLUSTER_SECONDARY_PASS } as any,
                            ],
                            {
                                redisOptions: {
                                    password: REDIS_CLUSTER_PRIMARY_PASS || REDIS_CLUSTER_SECONDARY_PASS,
                                    tls: REDIS_CLUSTER_ENABLE_SSL
                                        ? {
                                              rejectUnauthorized: false,
                                          }
                                        : undefined,
                                },
                            },
                        );
                    },
                    inject: [ConfigService],
                },
                RedisService,
            ],
            exports: [RedisService],
        };
    }
}
