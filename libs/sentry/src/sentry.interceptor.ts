import {
    BadRe<PERSON>Exception,
    CallHandler,
    ContextType,
    ExecutionContext,
    Injectable,
    NestInterceptor,
    UnauthorizedException,
} from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';
import { HealthCheckError } from '@nestjs/terminus';
import * as Sentry from '@sentry/nestjs';
import { ApiError } from 'libs/common/api-errors';
import { Observable } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';

@Injectable()
export class SentryInterceptor implements NestInterceptor {
    private readonly EXCEPTION_IGNORE = [UnauthorizedException, BadRequestException, HealthCheckError, ApiError];

    intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
        // Use request path + method for tracing name
        const protocol = context.getType();
        let spanName = 'default';

        switch (protocol) {
            case 'http': {
                const reqHttp = context.switchToHttp().getRequest();
                spanName = `${reqHttp.method} ${reqHttp.url}`;
                break;
            }
            case 'graphql' as ContextType: {
                const ctx = GqlExecutionContext.create(context);
                const info = ctx.getInfo();
                spanName = `GraphQL: ${info.parentType.name}.${info.fieldName}`;
                break;
            }
            case 'rpc' as ContextType: {
                const ctx = context.switchToRpc();
                spanName = context?.getHandler()?.name;
                break;
            }
            default:
                break;
        }

        const span = Sentry.startInactiveSpan({
            op: protocol,
            name: spanName,
        });

        return next.handle().pipe(
            tap(() => {
                setTimeout(() => span.end(), 0);
            }),
            catchError((exception) => {
                span.end();
                if (process.env.STAGE !== 'local') {
                    const shouldIgnore = this.EXCEPTION_IGNORE.some((ignore) => exception instanceof ignore);
                    if (!shouldIgnore) {
                        Sentry.captureException(exception);
                    }
                }
                throw exception;
            }),
        );
    }
}
