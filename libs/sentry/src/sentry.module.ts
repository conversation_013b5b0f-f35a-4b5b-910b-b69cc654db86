import { DynamicModule } from '@nestjs/common';

import { SentryInterceptor } from './sentry.interceptor';
import { initSentry } from './utils';

export class SentryModule {
    static register(): DynamicModule {
        return {
            module: SentryModule,
            providers: [
                {
                    provide: SentryInterceptor,
                    useFactory: () => {
                        initSentry();
                        return new SentryInterceptor();
                    },
                    inject: [],
                },
            ],
            exports: [SentryInterceptor],
        };
    }
}
