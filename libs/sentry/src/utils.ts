import { BadRequestException } from '@nestjs/common';
import * as Sentry from '@sentry/nestjs';
import { appConfig } from 'libs/configs';
import { nodeProfilingIntegration } from '@sentry/profiling-node';

export function initSentry(): void {
    if (appConfig.STAGE == 'local') {
        return;
    }

    const isProd = appConfig.STAGE === 'prod' || appConfig.STAGE === 'production';

    const dsn: string = appConfig.SENTRY_DSN;
    if (!dsn) {
        return;
    }

    Sentry.init({
        dsn: dsn,
        integrations: [Sentry.httpIntegration({ spans: true }), nodeProfilingIntegration()],
        normalizeDepth: 11,
        tracesSampleRate: isProd ? 0.05 : 1,
        profilesSampleRate: isProd ? 0.05 : 1,
        profileLifecycle: 'trace',
        environment: appConfig.STAGE || 'local',
        sendDefaultPii: true,
    });
}
