import { Injectable } from '@nestjs/common';
import { connect, IClientPublishOptions, MqttClient } from 'mqtt';
import { ConfigService } from '@nestjs/config';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';

@Injectable()
export class MqttService {
    private mqttClient: MqttClient;

    constructor(
        private readonly configService: ConfigService,
        @InjectPinoLogger(MqttService.name)
        private readonly logger: PinoLogger,
    ) {
        const mqtt = {
            host: this.configService.get<string>('EMQX_HOST'),
            port: Number(this.configService.get<string>('EMQX_PORT')),
            protocol: this.configService.get<string>('EMQX_PROTOCOL'),
            username: this.configService.get<string>('EMQX_USER'),
            password: this.configService.get<string>('EMQX_PASS'),
        };
        const mqttUrl = `${mqtt.protocol}://${mqtt.host}:${mqtt.port}/mqtt`;
        this.mqttClient = connect(mqttUrl, {
            auth: mqtt.username + ':' + mqtt.password,
        });
        this.mqttClient.on('connect', (packet) => {
            this.logger.info('MQTT connected');
        });
        this.mqttClient.on('error', (packet) => {
            this.logger.error(packet.message, 'MQTT error: ');
        });
    }

    async publish(topic: string, message: any, options?: IClientPublishOptions) {
        return await this.mqttClient.publishAsync(topic, JSON.stringify(message), options);
    }
}
