// import * as Redis from 'ioredis'
// import { Cluster } from 'ioredis'
// import { MqttClient } from 'mqtt'

// export type Redis = Redis.Redis

export enum IndicatorRedisType {
    Redis = 'redis',
    Queue = 'queue',
}

export enum ServingStatus {
    UNKNOWN = 0,
    SERVING = 1,
    NOT_SERVING = 2,
}
// export type RedisCheckSettings =
//   | {
//       type: IndicatorRedisType
//       client: Redis
//       timeout?: number
//       memoryThreshold?: number
//     }
//   | {
//       type: 'cluster'
//       client: Cluster
//     }

export interface OptionGetContextConnection {
    strict: boolean;
}

// export interface MqttService {
//   getClient(): MqttClient
// }

export interface InternalHealthCheckResponse {
    status: ServingStatus;
}

export interface IHealthFile {
    heathCheck: 'ok';
    updated_date: number;
}
