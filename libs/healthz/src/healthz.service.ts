import { HttpStatus, Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { HealthIndicatorFunction, HealthIndicatorResult } from '@nestjs/terminus/dist/health-indicator';
import { existsSync, mkdirSync, unlinkSync, writeFileSync } from 'fs';

import { IHealthFile } from './healthz.interface';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';

@Injectable()
export class HealthzService implements OnModuleInit {
    constructor(
        @InjectPinoLogger(HealthzService.name)
        private readonly logger: PinoLogger,
    ) {}

    onModuleInit() {
        if (!existsSync(this.getHealthPathFile())) {
            mkdirSync(this.getFolderContainHealthFile(), { recursive: true });
        }
    }

    protected getFolderContainHealthFile(): string {
        return '/tmp';
    }

    protected getNameOfHealthFile(): string {
        return 'healthz';
    }

    protected getHealthPathFile(): string {
        return `${this.getFolderContainHealthFile()}/${this.getNameOfHealthFile()}`;
    }

    protected contentForHealthFile(): IHealthFile {
        return {
            heathCheck: 'ok',
            updated_date: new Date().getTime(),
        };
    }

    protected writeContentToHealthFile(): void {
        writeFileSync(this.getHealthPathFile(), JSON.stringify(this.contentForHealthFile()));
    }
    protected removeHealthFile(): void {
        if (existsSync(this.getHealthPathFile())) {
            unlinkSync(this.getHealthPathFile());
        }
    }

    async healthCheckByFile(healthIndicators: HealthIndicatorFunction[]): Promise<void> {
        try {
            const listResponse = await Promise.all(healthIndicators.map((fn) => fn()));

            const servicesDown: any[] = [];

            listResponse.forEach((res: HealthIndicatorResult) => {
                Object.keys(res).forEach((key) => {
                    if (res[key].status == 'down') {
                        servicesDown.push(res[key]);
                    }
                });
            });

            if (servicesDown.length) {
                this.logger.error('Health-check down');
                throw new Error(`Health-check ${servicesDown.join(', ')} down`);
            }

            this.writeContentToHealthFile();
        } catch (err) {
            this.logger.error({ err }, 'Health-check down');
            this.removeHealthFile();
        }
    }

    async healthCheck(healthIndicators: HealthIndicatorFunction[]) {
        const listResponse = await Promise.all(healthIndicators.map((fn) => fn()));

        const servicesDown: any[] = [];

        listResponse.forEach((res: HealthIndicatorResult) => {
            Object.keys(res).forEach((key) => {
                if (res[key].status == 'down') {
                    servicesDown.push(res[key]);
                }
            });
        });

        if (servicesDown.length) {
            this.logger.error('Health-check down');
            throw new Error(`Health-check ${servicesDown.join(', ')} down`);
        }

        return { status: HttpStatus.OK, message: 'Health-check is ok' };
    }
}
