import { Injectable } from '@nestjs/common';
import { HealthCheckError, HealthIndicator, HealthIndicatorResult, MongooseHealthIndicator } from '@nestjs/terminus';

@Injectable()
export class MongooseHealth extends HealthIndicator {
    constructor(private readonly mongooseService: MongooseHealthIndicator) {
        super();
    }

    async check(): Promise<HealthIndicatorResult> {
        try {
            const pingCheck = await this.mongooseService.pingCheck('mongodb');

            return pingCheck;
        } catch (e) {
            throw new HealthCheckError(
                'Mongoose connection error',
                this.getStatus('mongodb', false, {
                    message: 'Connection provider not found in application context',
                }),
            );
        }
    }
}
