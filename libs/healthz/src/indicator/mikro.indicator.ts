import { EntityManager } from '@mikro-orm/core';
import { Injectable } from '@nestjs/common';
import { HealthCheckError, HealthIndicator, HealthIndicatorResult, MikroOrmHealthIndicator } from '@nestjs/terminus';

@Injectable()
export class MikroOrmHealth extends HealthIndicator {
    constructor(private readonly mikroOrmIndicator: MikroOrmHealthIndicator) {
        super();
    }

    async check(): Promise<HealthIndicatorResult> {
        try {
            const pingCheck = await this.mikroOrmIndicator.pingCheck('postgresql');

            return pingCheck;
        } catch (e) {
            throw new HealthCheckError(
                'TypeOrm connection error',
                this.getStatus('postgresql', false, {
                    message: 'Connection provider not found in application context',
                }),
            );
        }
    }
}
