import { defineConfig } from '@mikro-orm/postgresql';
import { SqlHighlighter } from '@mikro-orm/sql-highlighter';
// import { TsMorphMetadataProvider } from '@mikro-orm/reflection';
import { Migrator } from '@mikro-orm/migrations';
import { EntityGenerator } from '@mikro-orm/entity-generator';
import { SeedManager } from '@mikro-orm/seeder';
import { dbConfig } from './libs/configs';

export default defineConfig({
    host: dbConfig.POSTGRES_HOST,
    port: dbConfig.POSTGRES_PORT,
    user: dbConfig.POSTGRES_USER,
    password: dbConfig.POSTGRES_PASS,
    dbName: dbConfig.POSTGRES_DB_NAME,
    // entities: ['./dist/**/*.entity.js'],
    entities: [__dirname + '/libs/**/*.entity.js'],
    entitiesTs: [__dirname + '/libs/**/*.entity.ts'],
    migrations: {
        glob: '!(*.d).{js,ts}', // how to match migration files (all .js and .ts files, but not .d.ts)
        pathTs: 'migrations',
        path: 'dist/migrations',
        allOrNothing: true, // run all migrations in current batch in master transaction
    },
    debug: false,
    highlighter: new SqlHighlighter(),
    // metadataProvider: TsMorphMetadataProvider,
    extensions: [Migrator, EntityGenerator, SeedManager],
    disableTransactions: false,
    allowGlobalContext: true,
    driverOptions: {
        connection: {
            ssl: dbConfig.POSTGRES_SSL
        }
    }
});
