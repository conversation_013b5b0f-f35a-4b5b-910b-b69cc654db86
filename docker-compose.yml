x-default: &default
  logging:
    driver: "json-file"
    options:
      max-size: "10M"
      max-file: "3"

volumes:
  postgres-data:
  redis-data:
  pgadmin4:

services:
  redis:
    <<: *default
    image: 'bitnami/redis:6.2.3'
    ports:
      - 6379:6379
    environment:
      # ALLOW_EMPTY_PASSWORD is recommended only for development.
      # - ALLOW_EMPTY_PASSWORD=yes
      - REDIS_PASSWORD=dev
    volumes:
      - 'redis-data:/bitnami/redis/data'

  postgres:
    << : *default
    image: postgres:16.7
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data/
    environment:
      - POSTGRES_DB=xbit
      - POSTGRES_USER=dev
      - POSTGRES_PASSWORD=dev
    command: ["postgres", "-c", "log_statement=all"]
