import { Wallet } from 'ethers';

// /**
//  * Sign a message using a private key
//  * @param privateKey - The private key of the wallet
//  * @param message - The message to sign
//  * @returns The signed message (signature)
//  */
async function signMessage(privateKey: string, message: string): Promise<string> {
    try {
        // Create a wallet instance from the private key
        const wallet = new Wallet(privateKey);

        console.log(wallet.address);

        // Sign the message
        const signature = await wallet.signMessage(message);

        return signature;
    } catch (error) {
        console.error('Error signing message:', error);
        throw error;
    }
}

// Example usage
(async () => {
    console.log('process.argv: ', process.argv);
    const args = process.argv.splice(2);
    const privateKey = args[0];
    let message = 'Welcome to Xbit!\n\n';
    message += 'Click to sign in and accept the Xbit Terms of Service https://xbit.com/terms-of-use\n\n';
    message += 'This request will not trigger a blockchain transaction or cost any gas fees.\n\n';
    message += `Wallet address:\n\n${args[1]}\n\n`;
    message += `Nonce:\n${args[2]}`;

    console.log('message: ', message);
    try {
        const signature = await signMessage(privateKey, message);
        console.log('Message:', message);
        console.log('Signature:', signature);
    } catch (error) {
        console.error('Failed to sign message:', error);
    }
})();
