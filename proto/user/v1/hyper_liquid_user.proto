syntax = "proto3";

package user.hyperliquid_user.v1;

service HyperLiquidUserService {
  rpc SignUserPlaceOrder(SignUserPlaceOrderRequest) returns (SignUserPlaceOrderResponse) {}
  rpc SignUserCancelOrder(SignUserCancelOrderRequest) returns (SignUserCancelOrderResponse) {}
}

message OrderRequest {
  string type = 1;
  repeated Order orders = 2;
  string grouping = 3;
  Builder builder = 4;

  message Order {
    double a = 1;
    bool b = 2;
    string p = 3;
    string s = 4;
    bool r = 5;
    OrderType t = 6;

    message OrderType {
      oneof type {
        Limit limit = 1;
        Trigger trigger = 2;
      }

      message Limit {
        string tif = 1;
      }

      message Trigger {
        bool isMarket = 1;
        string triggerPx = 2;
        string tpsl = 3;
      }
    }

    string c = 7;
  }

  message Builder {
    string b = 1;
    double f = 2;
  }
}

message SignUserPlaceOrderRequest {
  OrderRequest action = 1;
  int64 nonce = 2;
  optional string vault_address = 3;
  string user_id = 4;
}

message TransactionSignature {
  string r = 1;
  string s = 2;
  int64 v = 3;
}

message SignUserPlaceOrderResponse {
  OrderRequest action = 1;
  int64 nonce = 2;
  string user_id = 3;
  TransactionSignature signature = 4;
  optional string vault_address = 5;
}


message CancelAction {
  string type = 1; // e.g., "cancel"
  repeated Cancel cancels = 2;

  message Cancel {
    double a = 1; // Amount
    int64 o = 2;  // Order ID
  }
}

message SignUserCancelOrderRequest {
  CancelAction action = 1;
  int64 nonce = 2;
  optional string vault_address = 3;
  string user_id = 4;
}

message SignUserCancelOrderResponse {
  CancelAction action = 1;
  int64 nonce = 2;
  optional string vault_address = 3;
  string user_id = 4;
  TransactionSignature signature = 5;
}