// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v1.178.0
//   protoc               unknown
// source: user/v1/hyper_liquid_user.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";

export const protobufPackage = "user.hyperliquid_user.v1";

export interface OrderRequest {
  type: string;
  orders: OrderRequest_Order[];
  grouping: string;
  builder: OrderRequest_Builder | undefined;
}

export interface OrderRequest_Order {
  a: number;
  b: boolean;
  p: string;
  s: string;
  r: boolean;
  t: OrderRequest_Order_OrderType | undefined;
  c: string;
}

export interface OrderRequest_Order_OrderType {
  limit?: OrderRequest_Order_OrderType_Limit | undefined;
  trigger?: OrderRequest_Order_OrderType_Trigger | undefined;
}

export interface OrderRequest_Order_OrderType_Limit {
  tif: string;
}

export interface OrderRequest_Order_OrderType_Trigger {
  isMarket: boolean;
  triggerPx: string;
  tpsl: string;
}

export interface OrderRequest_Builder {
  b: string;
  f: number;
}

export interface SignUserPlaceOrderRequest {
  action: OrderRequest | undefined;
  nonce: number;
  vaultAddress?: string | undefined;
  userId: string;
}

export interface TransactionSignature {
  r: string;
  s: string;
  v: number;
}

export interface SignUserPlaceOrderResponse {
  action: OrderRequest | undefined;
  nonce: number;
  userId: string;
  signature: TransactionSignature | undefined;
  vaultAddress?: string | undefined;
}

export interface CancelAction {
  /** e.g., "cancel" */
  type: string;
  cancels: CancelAction_Cancel[];
}

export interface CancelAction_Cancel {
  /** Amount */
  a: number;
  /** Order ID */
  o: number;
}

export interface SignUserCancelOrderRequest {
  action: CancelAction | undefined;
  nonce: number;
  vaultAddress?: string | undefined;
  userId: string;
}

export interface SignUserCancelOrderResponse {
  action: CancelAction | undefined;
  nonce: number;
  vaultAddress?: string | undefined;
  userId: string;
  signature: TransactionSignature | undefined;
}

export const USER_HYPERLIQUID_USER_V1_PACKAGE_NAME = "user.hyperliquid_user.v1";

export interface HyperLiquidUserServiceClient {
  signUserPlaceOrder(request: SignUserPlaceOrderRequest, metadata?: Metadata): Observable<SignUserPlaceOrderResponse>;

  signUserCancelOrder(
    request: SignUserCancelOrderRequest,
    metadata?: Metadata,
  ): Observable<SignUserCancelOrderResponse>;
}

export interface HyperLiquidUserServiceController {
  signUserPlaceOrder(
    request: SignUserPlaceOrderRequest,
    metadata?: Metadata,
  ): Promise<SignUserPlaceOrderResponse> | Observable<SignUserPlaceOrderResponse> | SignUserPlaceOrderResponse;

  signUserCancelOrder(
    request: SignUserCancelOrderRequest,
    metadata?: Metadata,
  ): Promise<SignUserCancelOrderResponse> | Observable<SignUserCancelOrderResponse> | SignUserCancelOrderResponse;
}

export function HyperLiquidUserServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["signUserPlaceOrder", "signUserCancelOrder"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("HyperLiquidUserService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("HyperLiquidUserService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const HYPER_LIQUID_USER_SERVICE_NAME = "HyperLiquidUserService";
