// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v1.178.0
//   protoc               unknown
// source: user/v1/hyper_liquid_vault.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";
import { Empty } from "../../google/protobuf/empty";

export const protobufPackage = "user.hyperliquid_vault.v1";

export interface MasterWalletResponse {
  id: string;
  address: string;
}

export interface CreateMasterWalletAgentRequest {
  masterWalletId: string;
}

export interface CreateMasterWalletAgentResponse {
  id: string;
  address: string;
  masterWalletId: string;
}

export interface OrderRequest {
  type: string;
  orders: OrderRequest_Order[];
  grouping: string;
  builder: OrderRequest_Builder | undefined;
}

export interface OrderRequest_Order {
  a: number;
  b: boolean;
  p: string;
  s: string;
  r: boolean;
  t: OrderRequest_Order_OrderType | undefined;
  c: string;
}

export interface OrderRequest_Order_OrderType {
  limit?: OrderRequest_Order_OrderType_Limit | undefined;
  trigger?: OrderRequest_Order_OrderType_Trigger | undefined;
}

export interface OrderRequest_Order_OrderType_Limit {
  tif: string;
}

export interface OrderRequest_Order_OrderType_Trigger {
  isMarket: boolean;
  triggerPx: string;
  tpsl: string;
}

export interface OrderRequest_Builder {
  b: string;
  f: number;
}

export interface SignPlaceOrderRequest {
  action: OrderRequest | undefined;
  nonce: number;
  vaultAddress?: string | undefined;
  masterWalletId: string;
}

export interface TransactionSignature {
  r: string;
  s: string;
  v: number;
}

export interface SignPlaceOrderResponse {
  action: OrderRequest | undefined;
  nonce: number;
  masterWalletId: string;
  signature: TransactionSignature | undefined;
  vaultAddress?: string | undefined;
}

export interface ImportMasterWalletRequest {
  privateKey: string;
}

export interface CancelAction {
  /** e.g., "cancel" */
  type: string;
  cancels: CancelAction_Cancel[];
}

export interface CancelAction_Cancel {
  /** Amount */
  a: number;
  /** Order ID */
  o: number;
}

export interface SignCancelOrderRequest {
  action: CancelAction | undefined;
  nonce: number;
  vaultAddress?: string | undefined;
  masterWalletId: string;
}

export interface SignCancelOrderResponse {
  action: CancelAction | undefined;
  nonce: number;
  vaultAddress?: string | undefined;
  masterWalletId: string;
  signature: TransactionSignature | undefined;
}

export const USER_HYPERLIQUID_VAULT_V1_PACKAGE_NAME = "user.hyperliquid_vault.v1";

export interface HyperLiquidVaultServiceClient {
  createMasterWallet(request: Empty, metadata?: Metadata): Observable<MasterWalletResponse>;

  createMasterWalletAgent(
    request: CreateMasterWalletAgentRequest,
    metadata?: Metadata,
  ): Observable<CreateMasterWalletAgentResponse>;

  signPlaceOrder(request: SignPlaceOrderRequest, metadata?: Metadata): Observable<SignPlaceOrderResponse>;

  importMasterWallet(request: ImportMasterWalletRequest, metadata?: Metadata): Observable<MasterWalletResponse>;

  signCancelOrder(request: SignCancelOrderRequest, metadata?: Metadata): Observable<SignCancelOrderResponse>;
}

export interface HyperLiquidVaultServiceController {
  createMasterWallet(
    request: Empty,
    metadata?: Metadata,
  ): Promise<MasterWalletResponse> | Observable<MasterWalletResponse> | MasterWalletResponse;

  createMasterWalletAgent(
    request: CreateMasterWalletAgentRequest,
    metadata?: Metadata,
  ):
    | Promise<CreateMasterWalletAgentResponse>
    | Observable<CreateMasterWalletAgentResponse>
    | CreateMasterWalletAgentResponse;

  signPlaceOrder(
    request: SignPlaceOrderRequest,
    metadata?: Metadata,
  ): Promise<SignPlaceOrderResponse> | Observable<SignPlaceOrderResponse> | SignPlaceOrderResponse;

  importMasterWallet(
    request: ImportMasterWalletRequest,
    metadata?: Metadata,
  ): Promise<MasterWalletResponse> | Observable<MasterWalletResponse> | MasterWalletResponse;

  signCancelOrder(
    request: SignCancelOrderRequest,
    metadata?: Metadata,
  ): Promise<SignCancelOrderResponse> | Observable<SignCancelOrderResponse> | SignCancelOrderResponse;
}

export function HyperLiquidVaultServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "createMasterWallet",
      "createMasterWalletAgent",
      "signPlaceOrder",
      "importMasterWallet",
      "signCancelOrder",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("HyperLiquidVaultService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("HyperLiquidVaultService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const HYPER_LIQUID_VAULT_SERVICE_NAME = "HyperLiquidVaultService";
