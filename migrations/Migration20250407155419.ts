import { Migration } from '@mikro-orm/migrations';

export class Migration20250407155419 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "perpetual_agent" add column "expired_at" bigint null;`);

    this.addSql(`alter table "user_perpetual_status" drop column "agent_expired_at";`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "perpetual_agent" drop column "expired_at";`);

    this.addSql(`alter table "user_perpetual_status" add column "agent_expired_at" bigint null;`);
  }

}
