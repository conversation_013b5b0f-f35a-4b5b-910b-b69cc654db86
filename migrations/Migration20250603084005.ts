import { Migration } from '@mikro-orm/migrations';

export class Migration20250603084005 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`drop index "user_telegram_id_index";`);

    this.addSql(`alter table "user" add constraint "user_telegram_id_unique" unique ("telegram_id");`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "user" drop constraint "user_telegram_id_unique";`);

    this.addSql(`create index "user_telegram_id_index" on "user" ("telegram_id");`);
  }

}
