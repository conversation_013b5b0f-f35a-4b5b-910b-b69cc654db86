import { Migration } from '@mikro-orm/migrations';

export class Migration20250618103053 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "user_embedded_wallet" drop constraint "user_embedded_wallet_user_id_chain_unique";`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "user_embedded_wallet" add constraint "user_embedded_wallet_user_id_chain_unique" unique ("user_id", "chain");`);
  }

}
