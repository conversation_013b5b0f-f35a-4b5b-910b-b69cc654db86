import { Migration } from '@mikro-orm/migrations';

export class Migration20250530072315 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`drop index "user_google_authenticator_index";`);

    this.addSql(`alter table "user_google_authenticator" add column "is_enabled" boolean not null default false;`);
    this.addSql(`alter table "user_google_authenticator" add constraint "user_google_authenticator_user_id_unique" unique ("user_id");`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "user_google_authenticator" drop constraint "user_google_authenticator_user_id_unique";`);
    this.addSql(`alter table "user_google_authenticator" drop column "is_enabled";`);

    this.addSql(`CREATE UNIQUE INDEX user_google_authenticator_index ON public.user_google_authenticator USING btree (user_id) WHERE (deleted_at IS NULL);`);
  }

}
