import { Migration } from '@mikro-orm/migrations';

export class Migration20250401073204 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`create table "user_perpetual_status" ("id" uuid not null, "user_id" uuid not null, "agent_expired_at" bigint null, "set_referral" boolean not null default false, "set_fee_builder" boolean not null default false, "created_at" timestamptz not null default now(), constraint "user_perpetual_status_pkey" primary key ("id"));`);
    this.addSql(`alter table "user_perpetual_status" add constraint "user_perpetual_status_user_id_unique" unique ("user_id");`);

    this.addSql(`alter table "user_perpetual_status" add constraint "user_perpetual_status_user_id_foreign" foreign key ("user_id") references "user" ("id") on update cascade;`);
  }

  override async down(): Promise<void> {
    this.addSql(`drop table if exists "user_perpetual_status" cascade;`);
  }

}
