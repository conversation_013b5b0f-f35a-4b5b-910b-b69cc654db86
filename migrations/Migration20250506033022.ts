import { Migration } from '@mikro-orm/migrations';

export class Migration20250506033022 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "snipe_tpslsetting" drop constraint "snipe_tpslsetting_setting_id_foreign";`);

    this.addSql(`alter table "auto_setting" drop constraint "auto_setting_setting_id_foreign";`);

    this.addSql(`alter table "auto_sell_setting" drop constraint "auto_sell_setting_setting_id_foreign";`);

    this.addSql(`alter table "auto_buy_setting" drop constraint "auto_buy_setting_setting_id_foreign";`);

    this.addSql(`alter table "snipe_tpslsetting" drop constraint "snipe_tpslsetting_setting_id_unique";`);
    this.addSql(`alter table "snipe_tpslsetting" drop column "setting_id";`);

    this.addSql(`alter table "auto_setting" drop constraint "auto_setting_setting_id_unique";`);
    this.addSql(`alter table "auto_setting" drop column "setting_id";`);

    this.addSql(`alter table "auto_sell_setting" drop constraint "auto_sell_setting_setting_id_unique";`);
    this.addSql(`alter table "auto_sell_setting" drop column "setting_id";`);

    this.addSql(`alter table "auto_buy_setting" drop constraint "auto_buy_setting_setting_id_unique";`);
    this.addSql(`alter table "auto_buy_setting" drop column "setting_id";`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "snipe_tpslsetting" add column "setting_id" uuid not null;`);
    this.addSql(`alter table "snipe_tpslsetting" add constraint "snipe_tpslsetting_setting_id_foreign" foreign key ("setting_id") references "sniper_bot_setting" ("id") on update cascade;`);
    this.addSql(`alter table "snipe_tpslsetting" add constraint "snipe_tpslsetting_setting_id_unique" unique ("setting_id");`);

    this.addSql(`alter table "auto_setting" add column "setting_id" uuid not null;`);
    this.addSql(`alter table "auto_setting" add constraint "auto_setting_setting_id_foreign" foreign key ("setting_id") references "sniper_bot_setting" ("id") on update cascade;`);
    this.addSql(`alter table "auto_setting" add constraint "auto_setting_setting_id_unique" unique ("setting_id");`);

    this.addSql(`alter table "auto_sell_setting" add column "setting_id" uuid not null;`);
    this.addSql(`alter table "auto_sell_setting" add constraint "auto_sell_setting_setting_id_foreign" foreign key ("setting_id") references "sniper_bot_setting" ("id") on update cascade;`);
    this.addSql(`alter table "auto_sell_setting" add constraint "auto_sell_setting_setting_id_unique" unique ("setting_id");`);

    this.addSql(`alter table "auto_buy_setting" add column "setting_id" uuid not null;`);
    this.addSql(`alter table "auto_buy_setting" add constraint "auto_buy_setting_setting_id_foreign" foreign key ("setting_id") references "sniper_bot_setting" ("id") on update cascade;`);
    this.addSql(`alter table "auto_buy_setting" add constraint "auto_buy_setting_setting_id_unique" unique ("setting_id");`);
  }

}
