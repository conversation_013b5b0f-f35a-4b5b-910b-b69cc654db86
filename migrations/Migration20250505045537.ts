import { Migration } from '@mikro-orm/migrations';

export class Migration20250505045537 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`create table "sniper_bot_setting" ("id" uuid not null, "user_id" uuid not null, "auto_slippage" boolean not null default true, "turbo_slippage" int not null default 25, "anti_mev_slippage" int not null default 25, "buy_tip" int not null, "sell_tip" int not null, "auto_buy" boolean not null default false, "auto_sell" boolean not null default false, "buy1" int not null default 0.1, "buy2" int not null default 0.5, "buy3" int not null default 1, "buy4" int not null default 3, "buy5" int not null default 5, "sell1" int not null default 50, "sell2" int not null default 100, "buy_xamount" int null default 0, "sell_xpercent" int null default 0, "created_at" timestamptz not null default now(), constraint "sniper_bot_setting_pkey" primary key ("id"));`);
    this.addSql(`alter table "sniper_bot_setting" add constraint "sniper_bot_setting_user_id_unique" unique ("user_id");`);

    this.addSql(`create table "snipe_tpslsetting" ("id" uuid not null, "setting_id" uuid not null, "enabled" boolean not null default false, "tp_ratio" int not null default 0, "sl_ratio" int not null default 0, "ttp_ratio" int not null default 0, "tp_sell_percent" int not null default 100, "sl_sell_percent" int not null default 100, "trailing_tp" boolean not null default false, "trailing_sl" boolean not null default false, "created_at" timestamptz not null default now(), constraint "snipe_tpslsetting_pkey" primary key ("id"));`);
    this.addSql(`alter table "snipe_tpslsetting" add constraint "snipe_tpslsetting_setting_id_unique" unique ("setting_id");`);

    this.addSql(`create table "auto_setting" ("id" uuid not null, "setting_id" uuid not null, "tip" int not null default 1, "slippage" int not null default 35, "snipe_raydium" int not null default 0.3, "anti_mev" boolean not null default false, "raydium_sell" int not null default 100, "snipe_tpsl" boolean not null default false, "dev_sell" int not null default 25, "sell" int not null default 100, "created_at" timestamptz not null default now(), constraint "auto_setting_pkey" primary key ("id"));`);
    this.addSql(`alter table "auto_setting" add constraint "auto_setting_setting_id_unique" unique ("setting_id");`);

    this.addSql(`create table "auto_sell_setting" ("id" uuid not null, "setting_id" uuid not null, "auto_sell" boolean not null, "take_profit_levels" jsonb not null, "slippage" int not null default 25, "expiration" text check ("expiration" in ('3d', '24h', '1h')) not null, "created_at" timestamptz not null default now(), constraint "auto_sell_setting_pkey" primary key ("id"));`);
    this.addSql(`alter table "auto_sell_setting" add constraint "auto_sell_setting_setting_id_unique" unique ("setting_id");`);

    this.addSql(`create table "auto_buy_setting" ("id" uuid not null, "setting_id" uuid not null, "auto_buy" boolean not null default false, "buy" int not null default 8, "slippage" int not null default 30, "min_liq" int not null default 0, "max_mc" int not null default 0, "created_at" timestamptz not null default now(), constraint "auto_buy_setting_pkey" primary key ("id"));`);
    this.addSql(`alter table "auto_buy_setting" add constraint "auto_buy_setting_setting_id_unique" unique ("setting_id");`);

    this.addSql(`alter table "sniper_bot_setting" add constraint "sniper_bot_setting_user_id_foreign" foreign key ("user_id") references "user" ("id") on update cascade;`);

    this.addSql(`alter table "snipe_tpslsetting" add constraint "snipe_tpslsetting_setting_id_foreign" foreign key ("setting_id") references "sniper_bot_setting" ("id") on update cascade;`);

    this.addSql(`alter table "auto_setting" add constraint "auto_setting_setting_id_foreign" foreign key ("setting_id") references "sniper_bot_setting" ("id") on update cascade;`);

    this.addSql(`alter table "auto_sell_setting" add constraint "auto_sell_setting_setting_id_foreign" foreign key ("setting_id") references "sniper_bot_setting" ("id") on update cascade;`);

    this.addSql(`alter table "auto_buy_setting" add constraint "auto_buy_setting_setting_id_foreign" foreign key ("setting_id") references "sniper_bot_setting" ("id") on update cascade;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "snipe_tpslsetting" drop constraint "snipe_tpslsetting_setting_id_foreign";`);

    this.addSql(`alter table "auto_setting" drop constraint "auto_setting_setting_id_foreign";`);

    this.addSql(`alter table "auto_sell_setting" drop constraint "auto_sell_setting_setting_id_foreign";`);

    this.addSql(`alter table "auto_buy_setting" drop constraint "auto_buy_setting_setting_id_foreign";`);

    this.addSql(`drop table if exists "sniper_bot_setting" cascade;`);

    this.addSql(`drop table if exists "snipe_tpslsetting" cascade;`);

    this.addSql(`drop table if exists "auto_setting" cascade;`);

    this.addSql(`drop table if exists "auto_sell_setting" cascade;`);

    this.addSql(`drop table if exists "auto_buy_setting" cascade;`);
  }

}
