import { Migration } from '@mikro-orm/migrations';

export class Migration20250227170708 extends Migration {
    override async up(): Promise<void> {
        this.addSql(
            `alter table "user_managed_wallet" alter column "encrypted_private_key" type text using ("encrypted_private_key"::text);`,
        );
    }

    override async down(): Promise<void> {
        this.addSql(
            `alter table "user_managed_wallet" alter column "encrypted_private_key" type varchar(255) using ("encrypted_private_key"::varchar(255));`,
        );
    }
}
