import { Migration } from '@mikro-orm/migrations';

export class Migration20250528094130 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "user_google_authenticator" drop constraint "user_google_authenticator_user_id_foreign";`);

    this.addSql(`alter table "user_google_authenticator" drop constraint "user_google_authenticator_pkey";`);
    this.addSql(`alter table "user_google_authenticator" drop column "is_enabled";`);

    this.addSql(`alter table "user_google_authenticator" add column "id" uuid not null default gen_random_uuid();`);
    this.addSql(`alter table "user_google_authenticator" add constraint "user_google_authenticator_user_id_foreign" foreign key ("user_id") references "user" ("id") on update cascade;`);
    this.addSql(`alter table "user_google_authenticator" add constraint "user_google_authenticator_pkey" primary key ("id");`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "user_google_authenticator" drop constraint "user_google_authenticator_user_id_foreign";`);

    this.addSql(`alter table "user_google_authenticator" drop constraint "user_google_authenticator_pkey";`);
    this.addSql(`alter table "user_google_authenticator" drop column "id";`);

    this.addSql(`alter table "user_google_authenticator" add column "is_enabled" bool not null default false;`);
    this.addSql(`alter table "user_google_authenticator" add constraint "user_google_authenticator_user_id_foreign" foreign key ("user_id") references "user" ("id") on update cascade on delete cascade;`);
    this.addSql(`alter table "user_google_authenticator" add constraint "user_google_authenticator_pkey" primary key ("user_id");`);
  }

}
