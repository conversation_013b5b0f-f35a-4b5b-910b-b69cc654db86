import { Migration } from '@mikro-orm/migrations';

export class Migration20250506033803 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`create index "snipe_tpslsetting_user_id_index" on "snipe_tpslsetting" ("user_id");`);

    this.addSql(`create index "auto_setting_user_id_index" on "auto_setting" ("user_id");`);

    this.addSql(`create index "auto_sell_setting_user_id_index" on "auto_sell_setting" ("user_id");`);

    this.addSql(`create index "auto_buy_setting_user_id_index" on "auto_buy_setting" ("user_id");`);
  }

  override async down(): Promise<void> {
    this.addSql(`drop index "snipe_tpslsetting_user_id_index";`);

    this.addSql(`drop index "auto_setting_user_id_index";`);

    this.addSql(`drop index "auto_sell_setting_user_id_index";`);

    this.addSql(`drop index "auto_buy_setting_user_id_index";`);
  }

}
