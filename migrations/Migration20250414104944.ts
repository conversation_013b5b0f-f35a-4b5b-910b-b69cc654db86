import { Migration } from '@mikro-orm/migrations';

export class Migration20250414104944 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`create table "vault_master_wallet" ("id" uuid not null, "address" varchar(255) not null, "encrypted_private_key" varchar(511) not null, "created_at" timestamptz not null default now(), constraint "vault_master_wallet_pkey" primary key ("id"));`);
    this.addSql(`alter table "vault_master_wallet" add constraint "vault_master_wallet_address_unique" unique ("address");`);

    this.addSql(`create table "vault_agent_wallet" ("id" uuid not null, "vault_master_wallet_id" uuid not null, "agent_address" varchar(255) not null, "encrypted_private_key" varchar(511) not null, "created_at" timestamptz not null default now(), constraint "vault_agent_wallet_pkey" primary key ("id"));`);
    this.addSql(`alter table "vault_agent_wallet" add constraint "vault_agent_wallet_vault_master_wallet_id_unique" unique ("vault_master_wallet_id");`);
    this.addSql(`alter table "vault_agent_wallet" add constraint "vault_agent_wallet_agent_address_unique" unique ("agent_address");`);

    this.addSql(`alter table "vault_agent_wallet" add constraint "vault_agent_wallet_vault_master_wallet_id_foreign" foreign key ("vault_master_wallet_id") references "vault_master_wallet" ("id") on update cascade;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "vault_agent_wallet" drop constraint "vault_agent_wallet_vault_master_wallet_id_foreign";`);

    this.addSql(`drop table if exists "vault_master_wallet" cascade;`);

    this.addSql(`drop table if exists "vault_agent_wallet" cascade;`);
  }

}
