import { Migration } from '@mikro-orm/migrations';

export class Migration20250506031942 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "snipe_tpslsetting" add column "user_id" uuid not null;`);
    this.addSql(`alter table "snipe_tpslsetting" add constraint "snipe_tpslsetting_user_id_foreign" foreign key ("user_id") references "user" ("id") on update cascade;`);
    this.addSql(`alter table "snipe_tpslsetting" add constraint "snipe_tpslsetting_user_id_unique" unique ("user_id");`);

    this.addSql(`alter table "auto_setting" add column "user_id" uuid not null;`);
    this.addSql(`alter table "auto_setting" add constraint "auto_setting_user_id_foreign" foreign key ("user_id") references "user" ("id") on update cascade;`);
    this.addSql(`alter table "auto_setting" add constraint "auto_setting_user_id_unique" unique ("user_id");`);

    this.addSql(`alter table "auto_sell_setting" add column "user_id" uuid not null;`);
    this.addSql(`alter table "auto_sell_setting" alter column "auto_sell" type boolean using ("auto_sell"::boolean);`);
    this.addSql(`alter table "auto_sell_setting" alter column "auto_sell" set default false;`);
    this.addSql(`alter table "auto_sell_setting" add constraint "auto_sell_setting_user_id_foreign" foreign key ("user_id") references "user" ("id") on update cascade;`);
    this.addSql(`alter table "auto_sell_setting" add constraint "auto_sell_setting_user_id_unique" unique ("user_id");`);

    this.addSql(`alter table "auto_buy_setting" add column "user_id" uuid not null;`);
    this.addSql(`alter table "auto_buy_setting" add constraint "auto_buy_setting_user_id_foreign" foreign key ("user_id") references "user" ("id") on update cascade;`);
    this.addSql(`alter table "auto_buy_setting" add constraint "auto_buy_setting_user_id_unique" unique ("user_id");`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "snipe_tpslsetting" drop constraint "snipe_tpslsetting_user_id_foreign";`);

    this.addSql(`alter table "auto_setting" drop constraint "auto_setting_user_id_foreign";`);

    this.addSql(`alter table "auto_sell_setting" drop constraint "auto_sell_setting_user_id_foreign";`);

    this.addSql(`alter table "auto_buy_setting" drop constraint "auto_buy_setting_user_id_foreign";`);

    this.addSql(`alter table "snipe_tpslsetting" drop constraint "snipe_tpslsetting_user_id_unique";`);
    this.addSql(`alter table "snipe_tpslsetting" drop column "user_id";`);

    this.addSql(`alter table "auto_setting" drop constraint "auto_setting_user_id_unique";`);
    this.addSql(`alter table "auto_setting" drop column "user_id";`);

    this.addSql(`alter table "auto_sell_setting" drop constraint "auto_sell_setting_user_id_unique";`);
    this.addSql(`alter table "auto_sell_setting" drop column "user_id";`);

    this.addSql(`alter table "auto_sell_setting" alter column "auto_sell" drop default;`);
    this.addSql(`alter table "auto_sell_setting" alter column "auto_sell" type boolean using ("auto_sell"::boolean);`);

    this.addSql(`alter table "auto_buy_setting" drop constraint "auto_buy_setting_user_id_unique";`);
    this.addSql(`alter table "auto_buy_setting" drop column "user_id";`);
  }

}
