import { Migration } from '@mikro-orm/migrations';

export class Migration20250528043532 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "notification_types" alter column "deleted_at" drop default;`);
    this.addSql(`alter table "notification_types" alter column "deleted_at" type timestamptz using ("deleted_at"::timestamptz);`);
    this.addSql(`alter table "notification_types" alter column "deleted_at" drop not null;`);

    this.addSql(`alter table "user_google_authenticator" alter column "deleted_at" drop default;`);
    this.addSql(`alter table "user_google_authenticator" alter column "deleted_at" type timestamptz using ("deleted_at"::timestamptz);`);
    this.addSql(`alter table "user_google_authenticator" alter column "deleted_at" drop not null;`);

    this.addSql(`alter table "user_notification_preferences" alter column "deleted_at" drop default;`);
    this.addSql(`alter table "user_notification_preferences" alter column "deleted_at" type timestamptz using ("deleted_at"::timestamptz);`);
    this.addSql(`alter table "user_notification_preferences" alter column "deleted_at" drop not null;`);

    this.addSql(`alter table "user_withdrawal_whitelist_addresses" alter column "deleted_at" drop default;`);
    this.addSql(`alter table "user_withdrawal_whitelist_addresses" alter column "deleted_at" type timestamptz using ("deleted_at"::timestamptz);`);
    this.addSql(`alter table "user_withdrawal_whitelist_addresses" alter column "deleted_at" drop not null;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "notification_types" alter column "deleted_at" type timestamptz(6) using ("deleted_at"::timestamptz(6));`);
    this.addSql(`alter table "notification_types" alter column "deleted_at" set default now();`);
    this.addSql(`alter table "notification_types" alter column "deleted_at" set not null;`);

    this.addSql(`alter table "user_google_authenticator" alter column "deleted_at" type timestamptz(6) using ("deleted_at"::timestamptz(6));`);
    this.addSql(`alter table "user_google_authenticator" alter column "deleted_at" set default now();`);
    this.addSql(`alter table "user_google_authenticator" alter column "deleted_at" set not null;`);

    this.addSql(`alter table "user_notification_preferences" alter column "deleted_at" type timestamptz(6) using ("deleted_at"::timestamptz(6));`);
    this.addSql(`alter table "user_notification_preferences" alter column "deleted_at" set default now();`);
    this.addSql(`alter table "user_notification_preferences" alter column "deleted_at" set not null;`);

    this.addSql(`alter table "user_withdrawal_whitelist_addresses" alter column "deleted_at" type timestamptz(6) using ("deleted_at"::timestamptz(6));`);
    this.addSql(`alter table "user_withdrawal_whitelist_addresses" alter column "deleted_at" set default now();`);
    this.addSql(`alter table "user_withdrawal_whitelist_addresses" alter column "deleted_at" set not null;`);
  }

}
