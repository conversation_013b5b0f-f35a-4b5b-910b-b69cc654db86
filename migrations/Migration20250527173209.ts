import { Migration } from '@mikro-orm/migrations';

export class Migration20250527173209 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`create table "notification_types" ("id" uuid not null default gen_random_uuid(), "category_code" varchar(100) not null, "category_name" varchar(255) not null, "description" text null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz not null default now(), constraint "notification_types_pkey" primary key ("id"));`);
    this.addSql(`alter table "notification_types" add constraint "notification_types_category_code_unique" unique ("category_code");`);

    this.addSql(`create table "user_google_authenticator" ("user_id" uuid not null, "secret_key" varchar(120) not null, "is_enabled" boolean not null default false, "revovery_codes_hashed" text null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz not null default now(), constraint "user_google_authenticator_pkey" primary key ("user_id"));`);
    this.addSql(`alter table "user_google_authenticator" add constraint "user_google_authenticator_user_id_unique" unique ("user_id");`);

    this.addSql(`create table "user_notification_preferences" ("id" uuid not null default gen_random_uuid(), "user_id" uuid not null, "notification_type_code" uuid not null, "channel" varchar(20) not null, "is_enabled" boolean not null default true, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz not null default now(), constraint "user_notification_preferences_pkey" primary key ("id"));`);
    this.addSql(`alter table "user_notification_preferences" add constraint "user_notification_preferences_user_id_notificatio_4f93b_unique" unique ("user_id", "notification_type_code", "channel");`);

    this.addSql(`create table "user_withdrawal_whitelist_addresses" ("id" uuid not null, "user_id" uuid not null, "address" varchar(60) not null, "nickname" varchar(60) null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz not null default now(), constraint "user_withdrawal_whitelist_addresses_pkey" primary key ("id"));`);

    this.addSql(`alter table "user_google_authenticator" add constraint "user_google_authenticator_user_id_foreign" foreign key ("user_id") references "user" ("id") on update cascade on delete cascade;`);

    this.addSql(`alter table "user_notification_preferences" add constraint "user_notification_preferences_user_id_foreign" foreign key ("user_id") references "user" ("id") on update cascade;`);
    this.addSql(`alter table "user_notification_preferences" add constraint "user_notification_preferences_notification_type_code_foreign" foreign key ("notification_type_code") references "notification_types" ("id") on update cascade;`);

    this.addSql(`alter table "user_withdrawal_whitelist_addresses" add constraint "user_withdrawal_whitelist_addresses_user_id_foreign" foreign key ("user_id") references "user" ("id") on update cascade;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "user_notification_preferences" drop constraint "user_notification_preferences_notification_type_code_foreign";`);

    this.addSql(`drop table if exists "notification_types" cascade;`);

    this.addSql(`drop table if exists "user_google_authenticator" cascade;`);

    this.addSql(`drop table if exists "user_notification_preferences" cascade;`);

    this.addSql(`drop table if exists "user_withdrawal_whitelist_addresses" cascade;`);
  }

}
