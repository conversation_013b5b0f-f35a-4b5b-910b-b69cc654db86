import { Migration } from '@mikro-orm/migrations';

export class Migration20250607081953 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "user" drop constraint if exists "user_auth_provider_check";`);

    this.addSql(`alter table "user" add column "google_id" varchar(255) null, add column "email" varchar(255) null, add column "avatar" varchar(255) null;`);
    this.addSql(`alter table "user" add constraint "user_auth_provider_check" check("auth_provider" in ('TELEGRAM', 'CHAIN_EVM', 'CHAIN_SOL', 'CHAIN_TRON', 'GOOGLE'));`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "user" drop constraint if exists "user_auth_provider_check";`);

    this.addSql(`alter table "user" drop column "google_id", drop column "email", drop column "avatar";`);

    this.addSql(`alter table "user" add constraint "user_auth_provider_check" check("auth_provider" in ('TELEGRAM', 'CHAIN_EVM', 'CHAIN_SOL', 'CHAIN_TRON'));`);
  }

}
