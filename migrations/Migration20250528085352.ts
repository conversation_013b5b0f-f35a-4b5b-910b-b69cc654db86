import { Migration } from '@mikro-orm/migrations';

export class Migration20250528085352 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "user_google_authenticator" drop constraint "user_google_authenticator_user_id_unique";`);

    this.addSql(`CREATE UNIQUE INDEX user_google_authenticator_index ON user_google_authenticator (user_id) WHERE deleted_at IS NULL;`);
  }

  override async down(): Promise<void> {
    this.addSql(`drop index "user_google_authenticator_index";`);

    this.addSql(`alter table "user_google_authenticator" add constraint "user_google_authenticator_user_id_unique" unique ("user_id");`);
  }

}
