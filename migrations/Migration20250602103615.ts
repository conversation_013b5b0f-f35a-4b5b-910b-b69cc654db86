import { Migration } from '@mikro-orm/migrations';

export class Migration20250602103615 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`create table "user_embedded_wallet" ("id" uuid not null, "user_id" uuid not null, "chain" text check ("chain" in ('EVM', 'SOLANA', 'TRON', 'ARB')) not null, "wallet_address" varchar(255) not null, "created_at" timestamptz not null default now(), constraint "user_embedded_wallet_pkey" primary key ("id"));`);
    this.addSql(`alter table "user_embedded_wallet" add constraint "user_embedded_wallet_wallet_address_chain_unique" unique ("wallet_address", "chain");`);
    this.addSql(`alter table "user_embedded_wallet" add constraint "user_embedded_wallet_user_id_chain_unique" unique ("user_id", "chain");`);

    this.addSql(`alter table "user_embedded_wallet" add constraint "user_embedded_wallet_user_id_foreign" foreign key ("user_id") references "user" ("id") on update cascade;`);
  }

  override async down(): Promise<void> {
    this.addSql(`drop table if exists "user_embedded_wallet" cascade;`);
  }

}
