import { Migration } from '@mikro-orm/migrations';

export class Migration20250311101436 extends Migration {
    override async up(): Promise<void> {
        this.addSql(
            `alter table "user" alter column "telegram_chat_id" type bigint using ("telegram_chat_id"::bigint);`,
        );
    }

    override async down(): Promise<void> {
        this.addSql(`alter table "user" alter column "telegram_chat_id" type int using ("telegram_chat_id"::int);`);
    }
}
