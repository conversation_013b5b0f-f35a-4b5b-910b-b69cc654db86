import { Migration } from '@mikro-orm/migrations';

export class Migration20250520085134 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "user_managed_wallet" drop constraint "user_managed_wallet_wallet_address_unique";`);

    this.addSql(`alter table "user_managed_wallet" add constraint "user_managed_wallet_wallet_address_chain_unique" unique ("wallet_address", "chain");`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "user_managed_wallet" drop constraint "user_managed_wallet_wallet_address_chain_unique";`);

    this.addSql(`alter table "user_managed_wallet" add constraint "user_managed_wallet_wallet_address_unique" unique ("wallet_address");`);
  }

}
