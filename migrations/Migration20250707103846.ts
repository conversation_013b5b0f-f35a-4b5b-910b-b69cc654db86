import { Migration } from '@mikro-orm/migrations';

export class Migration20250707103846 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`create table "sub_organization_auth" ("id" uuid not null, "user_id" uuid not null, "encrypted_private_key" varchar(511) not null, "public_key" varchar(511) not null, "sub_org_id" uuid not null, "created_at" timestamptz not null, constraint "sub_organization_auth_pkey" primary key ("id"));`);
    this.addSql(`alter table "sub_organization_auth" add constraint "sub_organization_auth_user_id_unique" unique ("user_id");`);
    this.addSql(`alter table "sub_organization_auth" add constraint "sub_organization_auth_encrypted_private_key_unique" unique ("encrypted_private_key");`);
    this.addSql(`alter table "sub_organization_auth" add constraint "sub_organization_auth_public_key_unique" unique ("public_key");`);
    this.addSql(`alter table "sub_organization_auth" add constraint "sub_organization_auth_sub_org_id_unique" unique ("sub_org_id");`);

    this.addSql(`alter table "sub_organization_auth" add constraint "sub_organization_auth_user_id_foreign" foreign key ("user_id") references "user" ("id") on update cascade;`);

    this.addSql(`alter table "user" add column "turnkey_version" varchar(255) not null default 'v1';`);
  }

  override async down(): Promise<void> {
    this.addSql(`drop table if exists "sub_organization_auth" cascade;`);

    this.addSql(`alter table "user" drop column "turnkey_version";`);
  }

}
