import { Migration } from '@mikro-orm/migrations';

export class Migration20250403110426 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`create table "perpetual_agent" ("id" uuid not null, "user_id" uuid not null, "user_address" varchar(255) not null, "agent_address" varchar(255) not null default false, "encrypted_private_key" text not null, "created_at" timestamptz not null default now(), constraint "perpetual_agent_pkey" primary key ("id"));`);
    this.addSql(`alter table "perpetual_agent" add constraint "perpetual_agent_agent_address_unique" unique ("agent_address");`);
    this.addSql(`create index "perpetual_agent_agent_address_index" on "perpetual_agent" ("agent_address");`);

    this.addSql(`alter table "perpetual_agent" add constraint "perpetual_agent_user_id_foreign" foreign key ("user_id") references "user" ("id") on update cascade;`);
  }

  override async down(): Promise<void> {
    this.addSql(`drop table if exists "perpetual_agent" cascade;`);
  }

}
