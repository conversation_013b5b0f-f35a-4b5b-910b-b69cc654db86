import { Migration } from '@mikro-orm/migrations';

export class Migration20250609100328 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "user" alter column "avatar" type text using ("avatar"::text);`);
    this.addSql(`alter table "user" add constraint "user_google_id_unique" unique ("google_id");`);
    this.addSql(`alter table "user" add constraint "user_email_unique" unique ("email");`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "user" drop constraint "user_google_id_unique";`);
    this.addSql(`alter table "user" drop constraint "user_email_unique";`);

    this.addSql(`alter table "user" alter column "avatar" type varchar(255) using ("avatar"::varchar(255));`);
  }

}
