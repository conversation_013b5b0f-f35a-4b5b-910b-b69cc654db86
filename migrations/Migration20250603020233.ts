import { Migration } from '@mikro-orm/migrations';

export class Migration20250603020233 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "user_embedded_wallet" add column "wallet_id" uuid not null, add column "wallet_account_id" uuid not null;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "user_embedded_wallet" drop column "wallet_id", drop column "wallet_account_id";`);
  }

}
