import { Migration } from '@mikro-orm/migrations';

export class Migration20250617094552 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "user" add column "is_exported_wallet" boolean not null default false;`);
    this.addSql(`alter table "user" add constraint "user_sub_org_id_unique" unique ("sub_org_id");`);
    this.addSql(`alter table "user" add constraint "user_turnkey_root_user_id_unique" unique ("turnkey_root_user_id");`);

    this.addSql(`alter table "user_embedded_wallet" add column "name" varchar(255) null;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "user" drop constraint "user_sub_org_id_unique";`);
    this.addSql(`alter table "user" drop constraint "user_turnkey_root_user_id_unique";`);
    this.addSql(`alter table "user" drop column "is_exported_wallet";`);

    this.addSql(`alter table "user_embedded_wallet" drop column "name";`);
  }

}
