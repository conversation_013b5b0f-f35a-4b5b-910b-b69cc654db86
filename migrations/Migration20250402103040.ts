import { Migration } from '@mikro-orm/migrations';

export class Migration20250402103040 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "user" drop constraint if exists "user_auth_provider_check";`);

    this.addSql(`alter table "user_managed_wallet" drop constraint if exists "user_managed_wallet_chain_check";`);

    this.addSql(`alter table "user" add constraint "user_auth_provider_check" check("auth_provider" in ('TELEGRAM', 'CHAIN_EVM', 'CHAIN_SOL', 'CHAIN_TRON', 'CHAIN_ARB'));`);

    this.addSql(`alter table "user_managed_wallet" add constraint "user_managed_wallet_chain_check" check("chain" in ('EVM', 'SOLANA', 'TRON', 'ARB'));`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "user" drop constraint if exists "user_auth_provider_check";`);

    this.addSql(`alter table "user_managed_wallet" drop constraint if exists "user_managed_wallet_chain_check";`);

    this.addSql(`alter table "user" add constraint "user_auth_provider_check" check("auth_provider" in ('TELEGRAM', 'CHAIN_EVM', 'CHAIN_SOL', 'CHAIN_TRON'));`);

    this.addSql(`alter table "user_managed_wallet" add constraint "user_managed_wallet_chain_check" check("chain" in ('EVM', 'SOLANA', 'TRON'));`);
  }

}
