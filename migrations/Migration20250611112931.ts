import { Migration } from '@mikro-orm/migrations';

export class Migration20250611112931 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "user" drop constraint if exists "user_auth_provider_check";`);

    this.addSql(`alter table "user" add constraint "user_auth_provider_check" check("auth_provider" in ('TELEGRAM', 'CHAIN_EVM', 'CHAIN_SOL', 'CHAIN_TRON', 'GOOGLE', 'EMAIL'));`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "user" drop constraint if exists "user_auth_provider_check";`);

    this.addSql(`alter table "user" add constraint "user_auth_provider_check" check("auth_provider" in ('TELEGRAM', 'CHAIN_EVM', 'CHAIN_SOL', 'CHAIN_TRON', 'GOOGLE'));`);
  }

}
