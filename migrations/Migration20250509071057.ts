import { Migration } from '@mikro-orm/migrations';

export class Migration20250509071057 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`drop table if exists "snipe_tpslsetting" cascade;`);

    this.addSql(`drop table if exists "sniper_bot_setting" cascade;`);

    this.addSql(`drop table if exists "auto_setting" cascade;`);

    this.addSql(`drop table if exists "auto_sell_setting" cascade;`);

    this.addSql(`drop table if exists "auto_buy_setting" cascade;`);

    this.addSql(`alter table "user" drop constraint "user_wallet_address_unique";`);

    this.addSql(`alter table "user" add constraint "user_wallet_address_auth_provider_unique" unique ("wallet_address", "auth_provider");`);
  }

  override async down(): Promise<void> {
    this.addSql(`create table "snipe_tpslsetting" ("id" uuid not null, "user_id" uuid not null, "enabled" boolean not null default false, "tp_ratio" real not null default 0, "sl_ratio" real not null default 0, "ttp_ratio" real not null default 0, "tp_sell_percent" real not null default 100, "sl_sell_percent" real not null default 100, "trailing_tp" boolean not null default false, "trailing_sl" boolean not null default false, "created_at" timestamptz not null default now(), constraint "snipe_tpslsetting_pkey" primary key ("id"));`);
    this.addSql(`create index "snipe_tpslsetting_user_id_index" on "snipe_tpslsetting" ("user_id");`);
    this.addSql(`alter table "snipe_tpslsetting" add constraint "snipe_tpslsetting_user_id_unique" unique ("user_id");`);

    this.addSql(`create table "sniper_bot_setting" ("id" uuid not null, "user_id" uuid not null, "auto_slippage" boolean not null default true, "turbo_slippage" real not null default 25, "anti_mev_slippage" real not null default 25, "buy_tip" real not null, "sell_tip" real not null, "auto_buy" boolean not null default false, "auto_sell" boolean not null default false, "buy1" real not null default 0.1, "buy2" real not null default 0.5, "buy3" real not null default 1, "buy4" real not null default 3, "buy5" real not null default 5, "sell1" real not null default 50, "sell2" real not null default 100, "buy_xamount" real not null default 0, "sell_xpercent" real not null default 0, "created_at" timestamptz not null default now(), constraint "sniper_bot_setting_pkey" primary key ("id"));`);
    this.addSql(`alter table "sniper_bot_setting" add constraint "sniper_bot_setting_user_id_unique" unique ("user_id");`);

    this.addSql(`create table "auto_setting" ("id" uuid not null, "user_id" uuid not null, "tip" real not null default 1, "slippage" real not null default 35, "snipe_raydium" real not null default 0.3, "anti_mev" boolean not null default false, "raydium_sell" real not null default 100, "snipe_tpsl" boolean not null default false, "dev_sell" real not null default 25, "sell" real not null default 100, "created_at" timestamptz not null default now(), constraint "auto_setting_pkey" primary key ("id"));`);
    this.addSql(`create index "auto_setting_user_id_index" on "auto_setting" ("user_id");`);
    this.addSql(`alter table "auto_setting" add constraint "auto_setting_user_id_unique" unique ("user_id");`);

    this.addSql(`create table "auto_sell_setting" ("id" uuid not null, "user_id" uuid not null, "auto_sell" boolean not null default false, "take_profit_levels" jsonb not null, "slippage" real not null default 25, "expiration" text check ("expiration" in ('3d', '24h', '1h')) not null, "created_at" timestamptz not null default now(), constraint "auto_sell_setting_pkey" primary key ("id"));`);
    this.addSql(`create index "auto_sell_setting_user_id_index" on "auto_sell_setting" ("user_id");`);
    this.addSql(`alter table "auto_sell_setting" add constraint "auto_sell_setting_user_id_unique" unique ("user_id");`);

    this.addSql(`create table "auto_buy_setting" ("id" uuid not null, "user_id" uuid not null, "auto_buy" boolean not null default false, "buy" real not null default 8, "slippage" real not null default 30, "min_liq" real not null default 0, "max_mc" real not null default 0, "created_at" timestamptz not null default now(), constraint "auto_buy_setting_pkey" primary key ("id"));`);
    this.addSql(`create index "auto_buy_setting_user_id_index" on "auto_buy_setting" ("user_id");`);
    this.addSql(`alter table "auto_buy_setting" add constraint "auto_buy_setting_user_id_unique" unique ("user_id");`);

    this.addSql(`alter table "snipe_tpslsetting" add constraint "snipe_tpslsetting_user_id_foreign" foreign key ("user_id") references "user" ("id") on update cascade;`);

    this.addSql(`alter table "sniper_bot_setting" add constraint "sniper_bot_setting_user_id_foreign" foreign key ("user_id") references "user" ("id") on update cascade;`);

    this.addSql(`alter table "auto_setting" add constraint "auto_setting_user_id_foreign" foreign key ("user_id") references "user" ("id") on update cascade;`);

    this.addSql(`alter table "auto_sell_setting" add constraint "auto_sell_setting_user_id_foreign" foreign key ("user_id") references "user" ("id") on update cascade;`);

    this.addSql(`alter table "auto_buy_setting" add constraint "auto_buy_setting_user_id_foreign" foreign key ("user_id") references "user" ("id") on update cascade;`);

    this.addSql(`alter table "user" drop constraint "user_wallet_address_auth_provider_unique";`);

    this.addSql(`alter table "user" add constraint "user_wallet_address_unique" unique ("wallet_address");`);
  }

}
