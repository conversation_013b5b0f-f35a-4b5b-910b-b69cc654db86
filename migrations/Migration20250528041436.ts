import { Migration } from '@mikro-orm/migrations';

export class Migration20250528041436 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "user_google_authenticator" alter column "secret_key" type varchar(100) using ("secret_key"::varchar(100));`);

    this.addSql(`alter table "user_managed_wallet" alter column "chain" type varchar(15) using ("chain"::varchar(15));`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "user_google_authenticator" alter column "secret_key" type varchar(120) using ("secret_key"::varchar(120));`);

    this.addSql(`alter table "user_managed_wallet" alter column "chain" type text using ("chain"::text);`);
  }

}
