import { Migration } from '@mikro-orm/migrations';

export class Migration20250411095534 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "user" drop column "favorite_tokens";`);

    this.addSql(`alter table "perpetual_agent" alter column "encrypted_private_key" type varchar(511) using ("encrypted_private_key"::varchar(511));`);

    this.addSql(`alter table "user_managed_wallet" alter column "encrypted_private_key" type varchar(511) using ("encrypted_private_key"::varchar(511));`);

    this.addSql(`alter table "user_perpetual_status" add column "referral_code" varchar(55) null, add column "fee_builder_address" varchar(255) null, add column "fee_builder_percent" numeric(10,0) null;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "user" add column "favorite_tokens" text[] not null default '{}';`);

    this.addSql(`alter table "perpetual_agent" alter column "encrypted_private_key" type text using ("encrypted_private_key"::text);`);

    this.addSql(`alter table "user_managed_wallet" alter column "encrypted_private_key" type text using ("encrypted_private_key"::text);`);

    this.addSql(`alter table "user_perpetual_status" drop column "referral_code", drop column "fee_builder_address", drop column "fee_builder_percent";`);
  }

}
