import { Migration } from '@mikro-orm/migrations';

export class Migration20250227150818 extends Migration {
    override async up(): Promise<void> {
        this.addSql(
            `create table "user_managed_wallet" ("id" uuid not null, "user_id" uuid not null, "wallet_address" varchar(255) not null, "chain" text check ("chain" in ('EVM', 'SOLANA', 'TRON')) not null, "encrypted_private_key" varchar(255) not null, "created_at" timestamptz not null default now(), constraint "user_managed_wallet_pkey" primary key ("id"));`,
        );
        this.addSql(
            `alter table "user_managed_wallet" add constraint "user_managed_wallet_wallet_address_unique" unique ("wallet_address");`,
        );

        this.addSql(
            `alter table "user_managed_wallet" add constraint "user_managed_wallet_user_id_foreign" foreign key ("user_id") references "user" ("id") on update cascade;`,
        );
    }

    override async down(): Promise<void> {
        this.addSql(`drop table if exists "user_managed_wallet" cascade;`);
    }
}
