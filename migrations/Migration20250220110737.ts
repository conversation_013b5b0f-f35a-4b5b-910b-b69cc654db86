import { Migration } from '@mikro-orm/migrations';

export class Migration20250220110737 extends Migration {
    override async up(): Promise<void> {
        this.addSql(`alter table "user" add column "telegram_chat_id" int null;`);
        this.addSql(`alter table "user" add constraint "user_telegram_chat_id_unique" unique ("telegram_chat_id");`);
    }

    override async down(): Promise<void> {
        this.addSql(`alter table "user" drop constraint "user_telegram_chat_id_unique";`);
        this.addSql(`alter table "user" drop column "telegram_chat_id";`);
    }
}
