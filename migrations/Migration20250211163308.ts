import { Migration } from '@mikro-orm/migrations';

export class Migration20250211163308 extends Migration {
    override async up(): Promise<void> {
        this.addSql(
            `create table "user" ("id" uuid not null, "wallet_address" varchar(255) not null, "name" varchar(255) null, "created_at" timestamptz not null default now(), constraint "user_pkey" primary key ("id"));`,
        );
        this.addSql(`alter table "user" add constraint "user_wallet_address_unique" unique ("wallet_address");`);
    }

    override async down(): Promise<void> {
        this.addSql(`drop table if exists "user" cascade;`);
    }
}
