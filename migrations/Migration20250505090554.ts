import { Migration } from '@mikro-orm/migrations';

export class Migration20250505090554 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "sniper_bot_setting" alter column "turbo_slippage" type real using ("turbo_slippage"::real);`);
    this.addSql(`alter table "sniper_bot_setting" alter column "anti_mev_slippage" type real using ("anti_mev_slippage"::real);`);
    this.addSql(`alter table "sniper_bot_setting" alter column "buy_tip" type real using ("buy_tip"::real);`);
    this.addSql(`alter table "sniper_bot_setting" alter column "sell_tip" type real using ("sell_tip"::real);`);
    this.addSql(`alter table "sniper_bot_setting" alter column "buy1" type real using ("buy1"::real);`);
    this.addSql(`alter table "sniper_bot_setting" alter column "buy2" type real using ("buy2"::real);`);
    this.addSql(`alter table "sniper_bot_setting" alter column "buy3" type real using ("buy3"::real);`);
    this.addSql(`alter table "sniper_bot_setting" alter column "buy4" type real using ("buy4"::real);`);
    this.addSql(`alter table "sniper_bot_setting" alter column "buy5" type real using ("buy5"::real);`);
    this.addSql(`alter table "sniper_bot_setting" alter column "sell1" type real using ("sell1"::real);`);
    this.addSql(`alter table "sniper_bot_setting" alter column "sell2" type real using ("sell2"::real);`);
    this.addSql(`alter table "sniper_bot_setting" alter column "buy_xamount" type real using ("buy_xamount"::real);`);
    this.addSql(`alter table "sniper_bot_setting" alter column "buy_xamount" set not null;`);
    this.addSql(`alter table "sniper_bot_setting" alter column "sell_xpercent" type real using ("sell_xpercent"::real);`);
    this.addSql(`alter table "sniper_bot_setting" alter column "sell_xpercent" set not null;`);

    this.addSql(`alter table "snipe_tpslsetting" alter column "tp_ratio" type real using ("tp_ratio"::real);`);
    this.addSql(`alter table "snipe_tpslsetting" alter column "sl_ratio" type real using ("sl_ratio"::real);`);
    this.addSql(`alter table "snipe_tpslsetting" alter column "ttp_ratio" type real using ("ttp_ratio"::real);`);
    this.addSql(`alter table "snipe_tpslsetting" alter column "tp_sell_percent" type real using ("tp_sell_percent"::real);`);
    this.addSql(`alter table "snipe_tpslsetting" alter column "sl_sell_percent" type real using ("sl_sell_percent"::real);`);

    this.addSql(`alter table "auto_setting" alter column "tip" type real using ("tip"::real);`);
    this.addSql(`alter table "auto_setting" alter column "slippage" type real using ("slippage"::real);`);
    this.addSql(`alter table "auto_setting" alter column "snipe_raydium" type real using ("snipe_raydium"::real);`);
    this.addSql(`alter table "auto_setting" alter column "raydium_sell" type real using ("raydium_sell"::real);`);
    this.addSql(`alter table "auto_setting" alter column "dev_sell" type real using ("dev_sell"::real);`);
    this.addSql(`alter table "auto_setting" alter column "sell" type real using ("sell"::real);`);

    this.addSql(`alter table "auto_sell_setting" alter column "slippage" type real using ("slippage"::real);`);

    this.addSql(`alter table "auto_buy_setting" alter column "buy" type real using ("buy"::real);`);
    this.addSql(`alter table "auto_buy_setting" alter column "slippage" type real using ("slippage"::real);`);
    this.addSql(`alter table "auto_buy_setting" alter column "min_liq" type real using ("min_liq"::real);`);
    this.addSql(`alter table "auto_buy_setting" alter column "max_mc" type real using ("max_mc"::real);`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "sniper_bot_setting" alter column "turbo_slippage" type int using ("turbo_slippage"::int);`);
    this.addSql(`alter table "sniper_bot_setting" alter column "anti_mev_slippage" type int using ("anti_mev_slippage"::int);`);
    this.addSql(`alter table "sniper_bot_setting" alter column "buy_tip" type int using ("buy_tip"::int);`);
    this.addSql(`alter table "sniper_bot_setting" alter column "sell_tip" type int using ("sell_tip"::int);`);
    this.addSql(`alter table "sniper_bot_setting" alter column "buy1" type int using ("buy1"::int);`);
    this.addSql(`alter table "sniper_bot_setting" alter column "buy2" type int using ("buy2"::int);`);
    this.addSql(`alter table "sniper_bot_setting" alter column "buy3" type int using ("buy3"::int);`);
    this.addSql(`alter table "sniper_bot_setting" alter column "buy4" type int using ("buy4"::int);`);
    this.addSql(`alter table "sniper_bot_setting" alter column "buy5" type int using ("buy5"::int);`);
    this.addSql(`alter table "sniper_bot_setting" alter column "sell1" type int using ("sell1"::int);`);
    this.addSql(`alter table "sniper_bot_setting" alter column "sell2" type int using ("sell2"::int);`);
    this.addSql(`alter table "sniper_bot_setting" alter column "buy_xamount" type int using ("buy_xamount"::int);`);
    this.addSql(`alter table "sniper_bot_setting" alter column "buy_xamount" drop not null;`);
    this.addSql(`alter table "sniper_bot_setting" alter column "sell_xpercent" type int using ("sell_xpercent"::int);`);
    this.addSql(`alter table "sniper_bot_setting" alter column "sell_xpercent" drop not null;`);

    this.addSql(`alter table "snipe_tpslsetting" alter column "tp_ratio" type int using ("tp_ratio"::int);`);
    this.addSql(`alter table "snipe_tpslsetting" alter column "sl_ratio" type int using ("sl_ratio"::int);`);
    this.addSql(`alter table "snipe_tpslsetting" alter column "ttp_ratio" type int using ("ttp_ratio"::int);`);
    this.addSql(`alter table "snipe_tpslsetting" alter column "tp_sell_percent" type int using ("tp_sell_percent"::int);`);
    this.addSql(`alter table "snipe_tpslsetting" alter column "sl_sell_percent" type int using ("sl_sell_percent"::int);`);

    this.addSql(`alter table "auto_setting" alter column "tip" type int using ("tip"::int);`);
    this.addSql(`alter table "auto_setting" alter column "slippage" type int using ("slippage"::int);`);
    this.addSql(`alter table "auto_setting" alter column "snipe_raydium" type int using ("snipe_raydium"::int);`);
    this.addSql(`alter table "auto_setting" alter column "raydium_sell" type int using ("raydium_sell"::int);`);
    this.addSql(`alter table "auto_setting" alter column "dev_sell" type int using ("dev_sell"::int);`);
    this.addSql(`alter table "auto_setting" alter column "sell" type int using ("sell"::int);`);

    this.addSql(`alter table "auto_sell_setting" alter column "slippage" type int using ("slippage"::int);`);

    this.addSql(`alter table "auto_buy_setting" alter column "buy" type int using ("buy"::int);`);
    this.addSql(`alter table "auto_buy_setting" alter column "slippage" type int using ("slippage"::int);`);
    this.addSql(`alter table "auto_buy_setting" alter column "min_liq" type int using ("min_liq"::int);`);
    this.addSql(`alter table "auto_buy_setting" alter column "max_mc" type int using ("max_mc"::int);`);
  }

}
