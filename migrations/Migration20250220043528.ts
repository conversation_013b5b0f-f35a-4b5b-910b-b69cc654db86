import { Migration } from '@mikro-orm/migrations';

export class Migration20250220043528 extends Migration {
    override async up(): Promise<void> {
        this.addSql(
            `alter table "user" alter column "wallet_address" type varchar(255) using ("wallet_address"::varchar(255));`,
        );
        this.addSql(`alter table "user" alter column "wallet_address" drop not null;`);
        this.addSql(`alter table "user" add constraint "user_telegram_id_unique" unique ("telegram_id");`);
    }

    override async down(): Promise<void> {
        this.addSql(`alter table "user" drop constraint "user_telegram_id_unique";`);

        this.addSql(
            `alter table "user" alter column "wallet_address" type varchar(255) using ("wallet_address"::varchar(255));`,
        );
        this.addSql(`alter table "user" alter column "wallet_address" set not null;`);
    }
}
