import { Migration } from '@mikro-orm/migrations';

export class Migration20250518075952 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "user" drop constraint if exists "user_auth_provider_check";`);

    this.addSql(`alter table "user" drop constraint "user_wallet_address_auth_provider_unique";`);

    this.addSql(`alter table "user" add column "referrer_code" varchar(255) null;`);
    this.addSql(`alter table "user" add constraint "user_auth_provider_check" check("auth_provider" in ('TELEGRAM', 'CHAIN_EVM', 'CHAIN_SOL', 'CHAIN_TRON'));`);
    this.addSql(`alter table "user" add constraint "user_wallet_address_unique" unique ("wallet_address");`);
    this.addSql(`alter table "user" add constraint "user_referrer_code_unique" unique ("referrer_code");`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "user" drop constraint if exists "user_auth_provider_check";`);

    this.addSql(`alter table "user" drop constraint "user_wallet_address_unique";`);
    this.addSql(`alter table "user" drop constraint "user_referrer_code_unique";`);
    this.addSql(`alter table "user" drop column "referrer_code";`);

    this.addSql(`alter table "user" add constraint "user_auth_provider_check" check("auth_provider" in ('TELEGRAM', 'CHAIN_EVM', 'CHAIN_SOL', 'CHAIN_TRON', 'CHAIN_ARB'));`);
    this.addSql(`alter table "user" add constraint "user_wallet_address_auth_provider_unique" unique ("wallet_address", "auth_provider");`);
  }

}
