import { Migration } from '@mikro-orm/migrations';

export class Migration20250227151931 extends Migration {
    override async up(): Promise<void> {
        this.addSql(
            `alter table "user_managed_wallet" alter column "chain" type varchar(15) using ("chain"::varchar(15));`,
        );
    }

    override async down(): Promise<void> {
        this.addSql(`alter table "user_managed_wallet" alter column "chain" type text using ("chain"::text);`);
    }
}
