import { Migration } from '@mikro-orm/migrations';

export class Migration20250227072014 extends Migration {
    override async up(): Promise<void> {
        this.addSql(
            `alter table "user" add column "auth_provider" text check ("auth_provider" in ('TELEGRAM', 'CHAIN_EVM', 'CHAIN_SOL', 'CHAIN_TRON')) not null;`,
        );
    }

    override async down(): Promise<void> {
        this.addSql(`alter table "user" drop column "auth_provider";`);
    }
}
