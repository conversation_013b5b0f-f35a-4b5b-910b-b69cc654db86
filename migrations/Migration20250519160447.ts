import { Migration } from '@mikro-orm/migrations';

export class Migration20250519160447 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`create table "user_device" ("id" uuid not null, "user_id" uuid not null, "fingerprint" varchar(255) not null, "created_at" timestamptz not null, constraint "user_device_pkey" primary key ("id"));`);
    this.addSql(`alter table "user_device" add constraint "user_device_user_id_fingerprint_unique" unique ("user_id", "fingerprint");`);

    this.addSql(`create table "activity_log" ("id" uuid not null, "user_id" uuid not null, "device_id" uuid null, "activity" varchar(255) not null, "created_at" timestamptz not null, constraint "activity_log_pkey" primary key ("id"));`);

    this.addSql(`alter table "user_device" add constraint "user_device_user_id_foreign" foreign key ("user_id") references "user" ("id") on update cascade;`);

    this.addSql(`alter table "activity_log" add constraint "activity_log_user_id_foreign" foreign key ("user_id") references "user" ("id") on update cascade;`);
    this.addSql(`alter table "activity_log" add constraint "activity_log_device_id_foreign" foreign key ("device_id") references "user_device" ("id") on update cascade on delete set null;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "activity_log" drop constraint "activity_log_device_id_foreign";`);

    this.addSql(`drop table if exists "user_device" cascade;`);

    this.addSql(`drop table if exists "activity_log" cascade;`);
  }

}
