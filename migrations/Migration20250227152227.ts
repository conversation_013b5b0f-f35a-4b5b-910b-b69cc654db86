import { Migration } from '@mikro-orm/migrations';

export class Migration20250227152227 extends Migration {
    override async up(): Promise<void> {
        this.addSql(
            `alter table "user_managed_wallet" add constraint "user_managed_wallet_user_id_chain_unique" unique ("user_id", "chain");`,
        );
    }

    override async down(): Promise<void> {
        this.addSql(`alter table "user_managed_wallet" drop constraint "user_managed_wallet_user_id_chain_unique";`);
    }
}
