import { Controller, Get, ServiceUnavailableException } from '@nestjs/common';
import { HealthzService } from 'libs/healthz/src';
import { MikroOrmHealth } from 'libs/healthz/src/indicator/mikro.indicator';

@Controller('api/user/healthz')
export class HealthzController {
    constructor(
        private readonly databaseHealth: MikroOrmHealth,
        private readonly healthzService: HealthzService,
    ) {}

    @Get()
    async check() {
        try {
            return await this.healthzService.healthCheck([async () => this.databaseHealth.check()]);
        } catch (e) {
            throw new ServiceUnavailableException({
                statusCode: 503,
                message: 'Health check failed',
            });
        }
    }
}
