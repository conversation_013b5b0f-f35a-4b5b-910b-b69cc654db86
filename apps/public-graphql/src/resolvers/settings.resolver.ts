import { Args, Context, Info, Mutation, Query, Resolver } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { GqlAuthGuard } from 'libs/internal/auth/auth.guard';
import { SettingsService } from 'libs/internal/settings/setting.service';
import { UserSettingsDTO } from 'libs/internal/settings/dto/user-settings.dto';
import { GraphQLResolveInfo, Kind } from 'graphql';
import { UpdatePreferenceInput } from 'libs/internal/settings/dto/user-notification-preference.dto';
import { SetupNew2FAResponse } from 'libs/internal/settings/dto/user-2fa.dto';

@Resolver()
@UseGuards(GqlAuthGuard)
export class SettingsResolver {
    constructor(private readonly settingsService: SettingsService) {}

    @Query(() => UserSettingsDTO)
    async userSettings(@Context() context: any, @Info() info: GraphQLResolveInfo): Promise<UserSettingsDTO> {
        const selections = info.fieldNodes[0].selectionSet?.selections;
        const selectedFields = selections
            ?.map((selection) => (selection.kind === Kind.FIELD ? selection.name.value : null))
            .filter(Boolean) as string[];

        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument, @typescript-eslint/no-unsafe-member-access
        return await this.settingsService.getUserSettings(context.req.user.sub, selectedFields);
    }

    @Mutation(() => Boolean)
    async updatePreference(@Context() context: any, @Args('input') input: UpdatePreferenceInput): Promise<boolean> {
        return await this.settingsService.updatePreference(context.req.user.sub, input);
    }

    @Query(() => SetupNew2FAResponse)
    async setupNew2FA(@Context() context: any): Promise<SetupNew2FAResponse> {
        return await this.settingsService.setupNew2FA(context.req.user.sub);
    }

    @Mutation(() => Boolean)
    async disable2FA(@Context() context: any, @Args('otpCode') otpCode: string): Promise<boolean> {
        return await this.settingsService.disable2FA(context.req.user.sub, otpCode);
    }

    @Query(() => Boolean)
    async verify2FA(@Context() context: any, @Args('code') code: string): Promise<boolean> {
        return await this.settingsService.verify2FA(context.req.user.sub, code);
    }
}
