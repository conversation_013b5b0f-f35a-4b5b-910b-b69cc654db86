import { Module } from '@nestjs/common';
import { AuthResolver } from './auth.resolver';
import { UsersResolver } from './users.resolver';
import { WalletResolver } from './wallet.resolver';
import { UsersModule } from 'libs/internal/users/users.module';

import { HyperLiquidModule } from 'libs/internal/hyper-liquid/hyper-liquid.module';
import { AuthModule } from 'libs/internal/auth/auth.module';
import { SettingsModule } from 'libs/internal/settings/setting.module';
import { SettingsResolver } from './settings.resolver';
import { TurnkeyModule } from '@lib/internal/turnkey';
import { RateLimitingModule } from 'libs/internal/rate-limiting';

@Module({
    imports: [AuthModule, UsersModule, SettingsModule, HyperLiquidModule, TurnkeyModule, RateLimitingModule],
    providers: [AuthResolver, UsersResolver, WalletResolver, SettingsResolver],
})
export class ResolversModule {}
