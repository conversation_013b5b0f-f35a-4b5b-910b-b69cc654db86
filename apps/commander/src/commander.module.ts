import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { PublishUserAddressCommand } from './commands/publish-user-address.command';
import { LoggerModule, LoggerService } from '@libs/logger';
import { MikroOrmModule } from '@mikro-orm/nestjs';
import defineConfig from '../../../mikro-orm.config';
import { NatsModule } from '../../../libs/internal/nats/nats.module';
import { UserEmbeddedWallet } from '../../../libs/internal/users/entities/user-embedded-wallet.entity';

@Module({
    imports: [
        LoggerModule.forRootAsync('commander'),
        MikroOrmModule.forRoot(defineConfig),
        MikroOrmModule.forFeature({
            entities: [UserEmbeddedWallet],
        }),
        NatsModule.registerAsync(),
    ],
    providers: [LoggerService, PublishUserAddressCommand],
})
export class CommanderModule {}
