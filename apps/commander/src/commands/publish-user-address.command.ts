import { Command, CommandRunner } from 'nest-commander';
import { LoggerService } from '@libs/logger';
import { UserEmbeddedWalletRepository } from '../../../../libs/internal/users/repositories/user-embedded-wallet.repository';
import { UserEmbeddedWallet } from '../../../../libs/internal/users/entities/user-embedded-wallet.entity';
import { UserEVMWallet, UserEVMWalletEvent } from '../../../../libs/internal/nats/nats.event';
import { SUBJECTS } from '../../../../libs/internal/nats/nats.subject';
import { NatsService } from '../../../../libs/internal/nats/nats.service';
import { ChainType } from '../../../../libs/internal/users/entities/user-managed-wallet.entity';

interface BasicCommandOptions {
    string?: string;
    boolean?: boolean;
    number?: number;
}

@Command({ name: 'publish-user-address', description: 'A parameter parse' })
export class PublishUserAddressCommand extends CommandRunner {
    constructor(
        private readonly natsService: NatsService,
        private readonly userEmbeddedWalletRepository: UserEmbeddedWalletRepository,
        private readonly logService: LoggerService,
    ) {
        super();
    }

    async run(passedParam: string[], options?: BasicCommandOptions): Promise<void> {
        this.logService.log('Start PublishUserAddressCommand with options', { options });
        this.publishToUserSyncService(await this.userEmbeddedWalletRepository.findAll());
    }

    publishToUserSyncService(wallets: UserEmbeddedWallet[]) {
        const transformedEVMWallets: UserEVMWallet[] = wallets
            .filter((wallet) => wallet.chain === ChainType.EVM)
            .map((wallet) => ({
                ID: wallet.id,
                UserID: wallet.user.id,
                WalletAddress: wallet.walletAddress,
                WalletID: wallet.walletId,
                WalletAccountID: wallet.walletAccountId,
                createdAt: wallet.createdAt,
            }));

        if (transformedEVMWallets.length == 0) {
            return;
        }
        this.natsService.publish<UserEVMWalletEvent>(SUBJECTS.USER_SYNC_INFO, {
            userWallets: transformedEVMWallets,
        });
    }
}
