import { Module } from '@nestjs/common';
import { HyperLiquidVaultController } from './hyper-liquid-vault.controller';
import { HyperLiquidModule } from 'libs/internal/hyper-liquid/hyper-liquid.module';
import { MikroOrmModule } from '@mikro-orm/nestjs';
import defineConfig from '../../../mikro-orm.config';
import { RedisModule } from '@lib/redis';
import { CachingModule } from 'libs/internal/caching/caching.module';
import { LoggerModule } from '@libs/logger';
import { HyperLiquidUserController } from './hyper-liquid-user.controller';
import { UserInfoController } from './user-info.controller';
import { UsersModule } from 'libs/internal/users/users.module';
import { AuthModule } from 'libs/internal/auth/auth.module';
import { UserSigningController } from './user-signing.controller';
import { JwtModule } from '@nestjs/jwt';
import { NatsModule } from 'libs/internal/nats/nats.module';
import { SettingsModule } from 'libs/internal/settings/setting.module';
import { UserSigningService } from './user-signing.service';
import { TurnkeyModule } from '@lib/internal/turnkey';
import { SentryModule } from '@libs/sentry';

@Module({
    imports: [
        LoggerModule.forRootAsync('user_internal_grpc'),
        RedisModule.registerAsync(),
        MikroOrmModule.forRoot(defineConfig),
        CachingModule,
        HyperLiquidModule,
        UsersModule,
        SettingsModule,
        AuthModule,
        JwtModule.register({}),
        NatsModule.registerAsync(),
        TurnkeyModule,
        SentryModule.register(),
    ],
    controllers: [HyperLiquidVaultController, HyperLiquidUserController, UserInfoController, UserSigningController],
    providers: [UserSigningService],
})
export class InternalGrpcModule {}
