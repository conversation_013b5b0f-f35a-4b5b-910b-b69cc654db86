import { Controller, Get } from '@nestjs/common';
import {
    CreateMasterWalletAgentRequest,
    CreateMasterWalletAgentResponse,
    HyperLiquidVaultServiceControllerMethods,
    ImportMasterWalletRequest,
    MasterWalletResponse,
    SignCancelOrderRequest,
    SignCancelOrderResponse,
    SignPlaceOrderRequest,
    SignPlaceOrderResponse,
} from 'protogen/ts/user/v1/hyper_liquid_vault';
import { HyperLiquidService } from 'libs/internal/hyper-liquid/hyper-liquid.service';

@HyperLiquidVaultServiceControllerMethods()
@Controller()
export class HyperLiquidVaultController {
    constructor(private readonly hyperLiquidService: HyperLiquidService) {}

    async createMasterWallet(): Promise<MasterWalletResponse> {
        return this.hyperLiquidService.createVaultMasterWallet();
    }

    async createMasterWalletAgent(input: CreateMasterWalletAgentRequest): Promise<CreateMasterWalletAgentResponse> {
        return this.hyperLiquidService.createVaultAgentWallet(input);
    }

    async signPlaceOrder(input: SignPlaceOrderRequest): Promise<SignPlaceOrderResponse> {
        return this.hyperLiquidService.signPlaceOrder(input);
    }

    async importMasterWallet(input: ImportMasterWalletRequest): Promise<MasterWalletResponse> {
        return this.hyperLiquidService.importMasterWallet(input);
    }

    async signCancelOrder(input: SignCancelOrderRequest): Promise<SignCancelOrderResponse> {
        return this.hyperLiquidService.signCancelOrder(input);
    }
}
