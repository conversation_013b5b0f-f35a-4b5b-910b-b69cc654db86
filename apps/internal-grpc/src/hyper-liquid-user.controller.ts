import { Controller } from '@nestjs/common';
import {
    HyperLiquidUserServiceControllerMethods,
    SignUserCancelOrderRequest,
    SignUserCancelOrderResponse,
    SignUserPlaceOrderRequest,
    SignUserPlaceOrderResponse,
} from '@protogen/user/v1/hyper_liquid_user';
import { HyperLiquidService } from 'libs/internal/hyper-liquid/hyper-liquid.service';

@Controller()
@HyperLiquidUserServiceControllerMethods()
export class HyperLiquidUserController {
    constructor(private readonly hyperLiquidService: HyperLiquidService) {}

    async signUserPlaceOrder(request: SignUserPlaceOrderRequest): Promise<SignUserPlaceOrderResponse> {
        return this.hyperLiquidService.signUserPlaceOrder(request);
    }

    async signUserCancelOrder(request: SignUserCancelOrderRequest): Promise<SignUserCancelOrderResponse> {
        return this.hyperLiquidService.signUserCancelOrder(request);
    }
}
