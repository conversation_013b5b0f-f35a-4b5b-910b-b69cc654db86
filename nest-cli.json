{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "apps", "monorepo": true, "compilerOptions": {"deleteOutDir": true, "webpack": false}, "projects": {"auth": {"type": "library", "root": "libs/internal/auth", "entryFile": "index", "sourceRoot": "libs/internal/auth/src", "compilerOptions": {"tsConfigPath": "libs/internal/auth/tsconfig.lib.json"}}, "commander": {"type": "application", "root": "apps/commander", "entryFile": "main", "sourceRoot": "apps/commander/src", "compilerOptions": {"tsConfigPath": "apps/commander/tsconfig.app.json", "assets": [{"include": "../../../libs/i18n/locales/**/*", "outDir": "dist/apps/commander/libs/i18n/locales"}], "watchAssets": true}}, "internal-grpc": {"type": "application", "root": "apps/internal-grpc", "entryFile": "main", "sourceRoot": "apps/internal-grpc/src", "compilerOptions": {"tsConfigPath": "apps/internal-grpc/tsconfig.app.json", "assets": [{"include": "../../../libs/i18n/locales/**/*", "outDir": "dist/apps/internal-grpc/libs/i18n/locales"}], "watchAssets": true}}, "internal/turnkey": {"type": "library", "root": "libs/internal/turnkey", "entryFile": "index", "sourceRoot": "libs/internal/turnkey/src", "compilerOptions": {"tsConfigPath": "libs/internal/turnkey/tsconfig.lib.json"}}, "logger": {"type": "library", "root": "libs/logger", "entryFile": "index", "sourceRoot": "libs/logger/src", "compilerOptions": {"tsConfigPath": "libs/logger/tsconfig.lib.json"}}, "public-graphql": {"type": "application", "root": "apps/public-graphql", "entryFile": "main", "sourceRoot": "apps/public-graphql/src", "compilerOptions": {"tsConfigPath": "apps/public-graphql/tsconfig.app.json", "assets": [{"include": "../../../libs/i18n/locales/**/*", "outDir": "dist/apps/public-graphql/libs/i18n/locales"}], "watchAssets": true}}, "sentry": {"type": "library", "root": "libs/sentry", "entryFile": "index", "sourceRoot": "libs/sentry/src", "compilerOptions": {"tsConfigPath": "libs/sentry/tsconfig.lib.json"}}, "settings": {"type": "library", "root": "libs/internal/settings", "entryFile": "index", "sourceRoot": "libs/internal/settings/src", "compilerOptions": {"tsConfigPath": "libs/internal/settings/tsconfig.lib.json"}}, "vault-management": {"type": "library", "root": "libs/internal/vault-management", "entryFile": "index", "sourceRoot": "libs/internal/vault-management/src", "compilerOptions": {"tsConfigPath": "libs/internal/vault-management/tsconfig.lib.json"}}}}